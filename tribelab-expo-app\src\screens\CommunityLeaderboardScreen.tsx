import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Icon from "react-native-vector-icons/Ionicons";
import { useNavigation, useRoute } from "@react-navigation/native";

interface Member {
  id: string;
  name: string;
  avatar: string;
  rank: number;
  points: number;
  level: number;
  badges: Badge[];
  stats: {
    postsCreated: number;
    commentsPosted: number;
    likesReceived: number;
    coursesCompleted: number;
    helpfulAnswers: number;
    daysActive: number;
  };
  joinedDate: string;
  isCurrentUser: boolean;
}

interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  earnedDate: string;
}

const CommunityLeaderboardScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { communityId } = route.params as { communityId: string };

  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState<"week" | "month" | "all">("month");
  const [category, setCategory] = useState<"overall" | "posts" | "helpful" | "courses">("overall");

  useEffect(() => {
    fetchLeaderboard();
  }, [communityId, timeframe, category]);

  const fetchLeaderboard = async () => {
    try {
      setLoading(true);
      // TODO: Implement real leaderboard API call
      // For now, show empty state since backend doesn't have leaderboard yet
      await new Promise(resolve => setTimeout(resolve, 500));

      setMembers([]);
    } catch (error) {
      console.error("Failed to load leaderboard:", error);
      Alert.alert("Error", "Failed to load leaderboard");
    } finally {
      setLoading(false);
    }
  };

  const handleMemberPress = (member: Member) => {
    if (member.isCurrentUser) {
      // Navigate to user's own profile
      navigation.navigate("ProfileMain" as never);
    } else {
      // Show member details modal or navigate to member profile
      Alert.alert(
        member.name,
        `Level ${member.level} • ${member.points} points\n\nJoined: ${new Date(member.joinedDate).toLocaleDateString()}\nActive for ${member.stats.daysActive} days`,
        [{ text: "OK" }]
      );
    }
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return "trophy";
      case 2:
        return "medal";
      case 3:
        return "ribbon";
      default:
        return null;
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return "#FFD700";
      case 2:
        return "#C0C0C0";
      case 3:
        return "#CD7F32";
      default:
        return "#666";
    }
  };

  const currentUser = members.find(m => m.isCurrentUser);

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#000" />
          <Text style={styles.loadingText}>Loading leaderboard...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Leaderboard</Text>
        <TouchableOpacity>
          <Icon name="information-circle-outline" size={24} color="#000" />
        </TouchableOpacity>
      </View>

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.filterGroup}>
            <Text style={styles.filterLabel}>Timeframe:</Text>
            {["week", "month", "all"].map((period) => (
              <TouchableOpacity
                key={period}
                style={[
                  styles.filterButton,
                  timeframe === period && styles.filterButtonActive,
                ]}
                onPress={() => setTimeframe(period as any)}
              >
                <Text
                  style={[
                    styles.filterButtonText,
                    timeframe === period && styles.filterButtonTextActive,
                  ]}
                >
                  {period.charAt(0).toUpperCase() + period.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          
          <View style={styles.filterGroup}>
            <Text style={styles.filterLabel}>Category:</Text>
            {[
              { key: "overall", label: "Overall" },
              { key: "posts", label: "Posts" },
              { key: "helpful", label: "Helpful" },
              { key: "courses", label: "Courses" },
            ].map((cat) => (
              <TouchableOpacity
                key={cat.key}
                style={[
                  styles.filterButton,
                  category === cat.key && styles.filterButtonActive,
                ]}
                onPress={() => setCategory(cat.key as any)}
              >
                <Text
                  style={[
                    styles.filterButtonText,
                    category === cat.key && styles.filterButtonTextActive,
                  ]}
                >
                  {cat.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Current User Stats */}
        {currentUser && (
          <View style={styles.currentUserCard}>
            <Text style={styles.currentUserTitle}>Your Ranking</Text>
            <View style={styles.currentUserStats}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>#{currentUser.rank}</Text>
                <Text style={styles.statLabel}>Rank</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{currentUser.points}</Text>
                <Text style={styles.statLabel}>Points</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>Level {currentUser.level}</Text>
                <Text style={styles.statLabel}>Level</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{currentUser.badges.length}</Text>
                <Text style={styles.statLabel}>Badges</Text>
              </View>
            </View>
          </View>
        )}

        {/* Top 3 Podium */}
        <View style={styles.podiumContainer}>
          <Text style={styles.sectionTitle}>Top Contributors</Text>
          <View style={styles.podium}>
            {members.slice(0, 3).map((member, index) => (
              <TouchableOpacity
                key={member.id}
                style={[styles.podiumItem, { order: index === 0 ? 2 : index === 1 ? 1 : 3 }]}
                onPress={() => handleMemberPress(member)}
              >
                <View style={styles.podiumRank}>
                  <Icon
                    name={getRankIcon(member.rank)!}
                    size={24}
                    color={getRankColor(member.rank)}
                  />
                </View>
                <Image source={{ uri: member.avatar }} style={styles.podiumAvatar} />
                <Text style={styles.podiumName}>{member.name}</Text>
                <Text style={styles.podiumPoints}>{member.points} pts</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Full Leaderboard */}
        <View style={styles.leaderboardContainer}>
          <Text style={styles.sectionTitle}>All Members</Text>
          {members.map((member) => (
            <TouchableOpacity
              key={member.id}
              style={[
                styles.memberCard,
                member.isCurrentUser && styles.currentUserMemberCard,
              ]}
              onPress={() => handleMemberPress(member)}
            >
              <View style={styles.memberRank}>
                <Text style={styles.memberRankText}>#{member.rank}</Text>
              </View>
              
              <Image source={{ uri: member.avatar }} style={styles.memberAvatar} />
              
              <View style={styles.memberInfo}>
                <View style={styles.memberHeader}>
                  <Text style={styles.memberName}>
                    {member.name} {member.isCurrentUser && "(You)"}
                  </Text>
                  <Text style={styles.memberLevel}>Level {member.level}</Text>
                </View>
                
                <Text style={styles.memberPoints}>{member.points} points</Text>
                
                {member.badges.length > 0 && (
                  <View style={styles.memberBadges}>
                    {member.badges.slice(0, 3).map((badge) => (
                      <View
                        key={badge.id}
                        style={[styles.badge, { backgroundColor: badge.color }]}
                      >
                        <Icon name={badge.icon} size={12} color="#fff" />
                      </View>
                    ))}
                    {member.badges.length > 3 && (
                      <Text style={styles.moreBadges}>+{member.badges.length - 3}</Text>
                    )}
                  </View>
                )}
              </View>
              
              <View style={styles.memberStats}>
                <Text style={styles.memberStatText}>
                  {member.stats.postsCreated} posts
                </Text>
                <Text style={styles.memberStatText}>
                  {member.stats.helpfulAnswers} helpful
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  filtersContainer: {
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  filterGroup: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    marginRight: 20,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: "500",
    marginRight: 10,
    color: "#666",
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    backgroundColor: "#f0f0f0",
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: "#000",
  },
  filterButtonText: {
    fontSize: 12,
    color: "#666",
  },
  filterButtonTextActive: {
    color: "#fff",
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: "#666",
  },
  currentUserCard: {
    margin: 20,
    padding: 20,
    backgroundColor: "#f9f9f9",
    borderRadius: 12,
  },
  currentUserTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 15,
    textAlign: "center",
  },
  currentUserStats: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  statItem: {
    alignItems: "center",
  },
  statValue: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#000",
  },
  statLabel: {
    fontSize: 12,
    color: "#666",
    marginTop: 2,
  },
  podiumContainer: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 20,
  },
  podium: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "flex-end",
    height: 200,
  },
  podiumItem: {
    alignItems: "center",
    marginHorizontal: 10,
    flex: 1,
  },
  podiumRank: {
    marginBottom: 10,
  },
  podiumAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginBottom: 8,
  },
  podiumName: {
    fontSize: 14,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: 4,
  },
  podiumPoints: {
    fontSize: 12,
    color: "#666",
  },
  leaderboardContainer: {
    padding: 20,
    paddingTop: 0,
  },
  memberCard: {
    flexDirection: "row",
    alignItems: "center",
    padding: 15,
    backgroundColor: "#f9f9f9",
    borderRadius: 12,
    marginBottom: 8,
  },
  currentUserMemberCard: {
    backgroundColor: "#E3F2FD",
    borderWidth: 1,
    borderColor: "#2196F3",
  },
  memberRank: {
    width: 30,
    alignItems: "center",
    marginRight: 15,
  },
  memberRankText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#666",
  },
  memberAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  memberInfo: {
    flex: 1,
  },
  memberHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 4,
  },
  memberName: {
    fontSize: 16,
    fontWeight: "600",
  },
  memberLevel: {
    fontSize: 12,
    color: "#666",
    backgroundColor: "#f0f0f0",
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  memberPoints: {
    fontSize: 14,
    color: "#333",
    marginBottom: 8,
  },
  memberBadges: {
    flexDirection: "row",
    alignItems: "center",
  },
  badge: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 4,
  },
  moreBadges: {
    fontSize: 10,
    color: "#666",
    marginLeft: 4,
  },
  memberStats: {
    alignItems: "flex-end",
  },
  memberStatText: {
    fontSize: 12,
    color: "#666",
    marginBottom: 2,
  },
});

export default CommunityLeaderboardScreen;
