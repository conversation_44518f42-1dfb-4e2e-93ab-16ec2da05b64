import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
  Alert,
  ActivityIndicator,
  RefreshControl,
  Modal,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Icon from "react-native-vector-icons/Ionicons";
import { useNavigation } from "@react-navigation/native";
import { useAppDispatch, useAppSelector } from "../hooks/redux";
import { fetchAdminUsers } from "../store/slices/adminSlice";
import Toast from "react-native-toast-message";

interface User {
  _id: string;
  name: string;
  email: string;
  profileImage?: string;
  role: "admin" | "platform_admin" | "user";
  status?: "active" | "suspended" | "banned";
  createdAt: string;
  lastActive?: string;
  points: number;
  level: number;
  stats?: {
    postsCount: number;
    commentsCount: number;
    communitiesJoined: number;
    coursesCompleted: number;
  };
  subscription: {
    plan: "free" | "pro" | "enterprise";
    status: "active" | "expired" | "cancelled";
    expiryDate?: string;
  };
}

interface UserAction {
  type: "suspend" | "ban" | "promote" | "demote" | "delete";
  reason: string;
  duration?: number; // days for suspension
}

const AdminUserManagementScreen = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { users, isLoading } = useAppSelector((state) => state.admin);

  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterRole, setFilterRole] = useState<"all" | "admin" | "platform_admin" | "user">("all");
  const [filterStatus, setFilterStatus] = useState<"all" | "active" | "suspended" | "banned">("all");
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showActionModal, setShowActionModal] = useState(false);
  const [actionForm, setActionForm] = useState<UserAction>({
    type: "suspend",
    reason: "",
    duration: 7,
  });

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      await dispatch(fetchAdminUsers({ page: 1 })).unwrap();
    } catch (error) {
      console.error("Failed to load users:", error);
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Failed to load users",
      });
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadUsers();
    setRefreshing(false);
  };

  const handleUserAction = async () => {
    if (!selectedUser || !actionForm.reason.trim()) {
      Alert.alert("Error", "Please provide a reason for this action");
      return;
    }

    try {
      // TODO: Implement real user action API calls
      Alert.alert(
        "Action Pending",
        `${actionForm.type} action for ${selectedUser.name} will be implemented with backend integration.`,
        [{ text: "OK" }]
      );

      setShowActionModal(false);
      setSelectedUser(null);
    } catch (error) {
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Failed to perform user action",
      });
    }
  };

  const handleUserPress = (user: User) => {
    setSelectedUser(user);
    setShowActionModal(true);
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "platform_admin":
        return "#ff4444";
      case "admin":
        return "#ff9800";
      case "user":
        return "#4CAF50";
      default:
        return "#666";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "#4CAF50";
      case "suspended":
        return "#ff9800";
      case "banned":
        return "#ff4444";
      default:
        return "#666";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatLastActive = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return "Just now";
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesRole = filterRole === "all" || user.role === filterRole;
    const matchesStatus = filterStatus === "all" || (user.status || "active") === filterStatus;

    return matchesSearch && matchesRole && matchesStatus;
  });

  if (isLoading && users.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#000" />
          <Text style={styles.loadingText}>Loading users...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>User Management</Text>
        <TouchableOpacity>
          <Icon name="add" size={24} color="#000" />
        </TouchableOpacity>
      </View>

      {/* Search and Filters */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Icon name="search" size={20} color="#666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search users..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </View>

      <View style={styles.filtersContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.filterGroup}>
            <Text style={styles.filterLabel}>Role:</Text>
            {["all", "admin", "moderator", "user"].map((role) => (
              <TouchableOpacity
                key={role}
                style={[
                  styles.filterButton,
                  filterRole === role && styles.filterButtonActive,
                ]}
                onPress={() => setFilterRole(role as any)}
              >
                <Text style={[
                  styles.filterButtonText,
                  filterRole === role && styles.filterButtonTextActive,
                ]}>
                  {role.charAt(0).toUpperCase() + role.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          
          <View style={styles.filterGroup}>
            <Text style={styles.filterLabel}>Status:</Text>
            {["all", "active", "suspended", "banned"].map((status) => (
              <TouchableOpacity
                key={status}
                style={[
                  styles.filterButton,
                  filterStatus === status && styles.filterButtonActive,
                ]}
                onPress={() => setFilterStatus(status as any)}
              >
                <Text style={[
                  styles.filterButtonText,
                  filterStatus === status && styles.filterButtonTextActive,
                ]}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>

      <ScrollView 
        style={styles.content} 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        <View style={styles.statsHeader}>
          <Text style={styles.statsText}>
            {filteredUsers.length} users found
          </Text>
        </View>

        {filteredUsers.map((user) => (
          <TouchableOpacity
            key={user._id}
            style={styles.userCard}
            onPress={() => handleUserPress(user)}
          >
            <Image
              source={{ uri: user.profileImage || "https://via.placeholder.com/50" }}
              style={styles.userAvatar}
            />

            <View style={styles.userInfo}>
              <View style={styles.userHeader}>
                <Text style={styles.userName}>{user.name}</Text>
                <View style={styles.userBadges}>
                  <View style={[styles.roleBadge, { backgroundColor: getRoleColor(user.role) }]}>
                    <Text style={styles.roleBadgeText}>{user.role}</Text>
                  </View>
                  <View style={[styles.statusBadge, { backgroundColor: getStatusColor(user.status || "active") }]}>
                    <Text style={styles.statusBadgeText}>{user.status || "active"}</Text>
                  </View>
                </View>
              </View>

              <Text style={styles.userEmail}>{user.email}</Text>

              <View style={styles.userMeta}>
                <Text style={styles.userMetaText}>
                  Joined {formatDate(user.createdAt)}
                </Text>
                <Text style={styles.userMetaText}>
                  Level {user.level} • {user.points} points
                </Text>
              </View>

              <View style={styles.userStats}>
                <Text style={styles.userStatsText}>
                  {user.stats?.postsCount || 0} posts • {user.stats?.commentsCount || 0} comments
                </Text>
              </View>
            </View>

            <Icon name="chevron-forward" size={20} color="#666" />
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Action Modal */}
      <Modal
        visible={showActionModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowActionModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowActionModal(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>User Actions</Text>
            <TouchableOpacity onPress={handleUserAction}>
              <Text style={styles.modalSaveText}>Confirm</Text>
            </TouchableOpacity>
          </View>
          
          {selectedUser && (
            <ScrollView style={styles.modalContent}>
              <View style={styles.userPreview}>
                <Image
                  source={{ uri: selectedUser.profileImage || "https://via.placeholder.com/50" }}
                  style={styles.previewAvatar}
                />
                <View>
                  <Text style={styles.previewName}>{selectedUser.name}</Text>
                  <Text style={styles.previewEmail}>{selectedUser.email}</Text>
                </View>
              </View>
              
              <View style={styles.actionSection}>
                <Text style={styles.sectionTitle}>Action Type</Text>
                {["suspend", "ban", "promote", "demote", "delete"].map((action) => (
                  <TouchableOpacity
                    key={action}
                    style={styles.actionOption}
                    onPress={() => setActionForm(prev => ({ ...prev, type: action as any }))}
                  >
                    <View style={[
                      styles.radioButton,
                      actionForm.type === action && styles.radioButtonSelected,
                    ]}>
                      {actionForm.type === action && (
                        <View style={styles.radioButtonInner} />
                      )}
                    </View>
                    <Text style={styles.actionOptionText}>
                      {action.charAt(0).toUpperCase() + action.slice(1)} User
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
              
              <View style={styles.reasonSection}>
                <Text style={styles.sectionTitle}>Reason *</Text>
                <TextInput
                  style={styles.reasonInput}
                  placeholder="Provide a reason for this action..."
                  value={actionForm.reason}
                  onChangeText={(text) => setActionForm(prev => ({ ...prev, reason: text }))}
                  multiline
                  numberOfLines={3}
                />
              </View>
              
              {actionForm.type === "suspend" && (
                <View style={styles.durationSection}>
                  <Text style={styles.sectionTitle}>Suspension Duration (days)</Text>
                  <TextInput
                    style={styles.durationInput}
                    placeholder="7"
                    value={actionForm.duration?.toString()}
                    onChangeText={(text) => setActionForm(prev => ({ 
                      ...prev, 
                      duration: parseInt(text) || 7 
                    }))}
                    keyboardType="numeric"
                  />
                </View>
              )}
            </ScrollView>
          )}
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f0f0f0",
    borderRadius: 25,
    paddingHorizontal: 15,
    paddingVertical: 10,
    gap: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: "#333",
  },
  filtersContainer: {
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  filterGroup: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    marginRight: 20,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: "500",
    marginRight: 10,
    color: "#666",
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    backgroundColor: "#f0f0f0",
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: "#000",
  },
  filterButtonText: {
    fontSize: 12,
    color: "#666",
  },
  filterButtonTextActive: {
    color: "#fff",
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: "#666",
  },
  statsHeader: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  statsText: {
    fontSize: 14,
    color: "#666",
  },
  userCard: {
    flexDirection: "row",
    alignItems: "center",
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  userAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  userInfo: {
    flex: 1,
  },
  userHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 4,
  },
  userName: {
    fontSize: 16,
    fontWeight: "600",
    flex: 1,
  },
  userBadges: {
    flexDirection: "row",
    gap: 5,
  },
  roleBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  roleBadgeText: {
    fontSize: 10,
    color: "#fff",
    fontWeight: "600",
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  statusBadgeText: {
    fontSize: 10,
    color: "#fff",
    fontWeight: "600",
  },
  userEmail: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  userMeta: {
    flexDirection: "row",
    gap: 15,
    marginBottom: 4,
  },
  userMetaText: {
    fontSize: 12,
    color: "#999",
  },
  userStats: {
    marginTop: 2,
  },
  userStatsText: {
    fontSize: 12,
    color: "#666",
  },
  modalContainer: {
    flex: 1,
    backgroundColor: "#fff",
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  modalCancelText: {
    fontSize: 16,
    color: "#666",
  },
  modalSaveText: {
    fontSize: 16,
    color: "#000",
    fontWeight: "600",
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  userPreview: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 30,
    padding: 15,
    backgroundColor: "#f9f9f9",
    borderRadius: 12,
  },
  previewAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 15,
  },
  previewName: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 4,
  },
  previewEmail: {
    fontSize: 14,
    color: "#666",
  },
  actionSection: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 15,
  },
  actionOption: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: "#ddd",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  radioButtonSelected: {
    borderColor: "#000",
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: "#000",
  },
  actionOptionText: {
    fontSize: 16,
    color: "#333",
  },
  reasonSection: {
    marginBottom: 25,
  },
  reasonInput: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    height: 80,
    textAlignVertical: "top",
  },
  durationSection: {
    marginBottom: 25,
  },
  durationInput: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
  },
});

export default AdminUserManagementScreen;
