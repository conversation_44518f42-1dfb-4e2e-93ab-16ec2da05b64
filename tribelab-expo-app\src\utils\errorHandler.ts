import Toast from "react-native-toast-message";
import { Alert } from "react-native";

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

export const handleApiError = (error: any, showToast: boolean = true): ApiError => {
  let errorMessage = "An unexpected error occurred";
  let status: number | undefined;
  let code: string | undefined;

  if (error.response) {
    // Server responded with error status
    status = error.response.status;
    errorMessage = error.response.data?.message || error.response.data?.error || errorMessage;
    code = error.response.data?.code;
  } else if (error.request) {
    // Network error
    errorMessage = "Network error. Please check your connection.";
    code = "NETWORK_ERROR";
  } else if (error.message) {
    // Other error
    errorMessage = error.message;
  }

  const apiError: ApiError = {
    message: errorMessage,
    status,
    code,
  };

  if (showToast) {
    Toast.show({
      type: "error",
      text1: "Error",
      text2: errorMessage,
      visibilityTime: 4000,
    });
  }

  console.error("API Error:", apiError);
  return apiError;
};

export const handleApiSuccess = (message: string, showToast: boolean = true) => {
  if (showToast) {
    Toast.show({
      type: "success",
      text1: "Success",
      text2: message,
      visibilityTime: 3000,
    });
  }
};

export const showConfirmDialog = (
  title: string,
  message: string,
  onConfirm: () => void,
  onCancel?: () => void
) => {
  Alert.alert(
    title,
    message,
    [
      {
        text: "Cancel",
        style: "cancel",
        onPress: onCancel,
      },
      {
        text: "Confirm",
        style: "destructive",
        onPress: onConfirm,
      },
    ]
  );
};

export const showInfoDialog = (title: string, message: string) => {
  Alert.alert(title, message, [{ text: "OK" }]);
};

// Loading state utilities
export const createLoadingState = () => ({
  isLoading: false,
  error: null as string | null,
});

export const setLoadingState = (state: any, loading: boolean, error?: string | null) => {
  state.isLoading = loading;
  state.error = error || null;
};

// Retry utilities
export const createRetryHandler = (
  retryFunction: () => Promise<void>,
  maxRetries: number = 3,
  delay: number = 1000
) => {
  let retryCount = 0;

  const retry = async (): Promise<void> => {
    try {
      await retryFunction();
    } catch (error) {
      retryCount++;
      if (retryCount < maxRetries) {
        setTimeout(retry, delay * retryCount);
      } else {
        throw error;
      }
    }
  };

  return retry;
};
