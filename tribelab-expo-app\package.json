{"name": "tribelab-expo-app", "main": "index.js", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "dev": "node dev-start.js", "backend": "cd backend && npm run dev", "backend:start": "cd backend && npm start"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-google-signin/google-signin": "^15.0.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@reduxjs/toolkit": "^2.8.2", "@types/lodash": "^4.17.20", "axios": "^1.10.0", "expo": "~53.0.17", "expo-application": "^6.1.5", "expo-auth-session": "^6.2.1", "expo-av": "^15.1.7", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-crypto": "~14.1.5", "expo-device": "^7.1.4", "expo-document-picker": "^13.1.6", "expo-file-system": "^18.1.11", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.7", "expo-local-authentication": "^16.0.5", "expo-media-library": "^17.1.7", "expo-notifications": "^0.31.4", "expo-router": "~5.1.3", "expo-secure-store": "^14.2.3", "expo-sharing": "^13.1.5", "expo-splash-screen": "~0.30.10", "expo-sqlite": "^15.2.14", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-video": "~2.2.2", "expo-web-browser": "~14.2.0", "lodash": "^4.17.21", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-animatable": "^1.4.0", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-modal": "^14.0.0-rc.1", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-super-grid": "^6.0.1", "react-native-svg": "^15.12.0", "react-native-toast-message": "^2.3.3", "react-native-web": "~0.20.0", "react-native-webview": "^13.13.5", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}