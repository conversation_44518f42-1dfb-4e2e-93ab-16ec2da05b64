{"version": 3, "file": "Errors.d.ts", "sourceRoot": "", "sources": ["../src/Errors.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAE/C;;GAEG;AACH,MAAM,MAAM,mBAAmB,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG;IACtD;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB,CAAC;AAEF,MAAM,MAAM,eAAe,GAAG,mBAAmB,GAAG;IAClD;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB,CAAC;AA6CF;;GAEG;AACH,qBAAa,aAAc,SAAQ,UAAU;IAC3C;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB;;;;OAIG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBAEnB,MAAM,EAAE,mBAAmB,EAAE,aAAa,EAAE,MAAM,GAAG,OAAO;CAgBzE;AAGD;;;;GAIG;AACH,qBAAa,SAAU,SAAQ,aAAa;IAC1C;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;gBAEH,QAAQ,EAAE,eAAe;CAItC;AAED;;GAEG;AACH,qBAAa,UAAW,SAAQ,aAAa;gBAC/B,QAAQ,EAAE,mBAAmB;CAG1C"}