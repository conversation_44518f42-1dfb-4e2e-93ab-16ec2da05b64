buildscript {
  def kotlin_version = rootProject.ext.has('kotlinVersion') ? rootProject.ext.get('kotlinVersion') : project.properties['RNGS_kotlinVersion']

  repositories {
    google()
    mavenCentral()
  }

  dependencies {
    classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:${kotlin_version}")
  }
}

apply plugin: 'com.android.library'
apply plugin: 'org.jetbrains.kotlin.android'

def isNewArchitectureEnabled() {
  // To opt-in for the New Architecture, you can either:
  // - Set `newArchEnabled` to true inside the `gradle.properties` file
  // - Invoke gradle with `-newArchEnabled=true`
  // - Set an environment variable `ORG_GRADLE_PROJECT_newArchEnabled=true`
  return project.hasProperty("newArchEnabled") && project.newArchEnabled == "true"
}

if (isNewArchitectureEnabled()) {
  apply plugin: "com.facebook.react"
}

def getExtOrIntegerDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : (project.properties['RNGS_' + name]).toInteger()
}
def safeExtGet(prop, fallback) {
  rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

android {
  def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
  if (agpVersion.tokenize('.')[0].toInteger() >= 7) {
    namespace "com.reactnativegooglesignin"
  }
  compileSdkVersion getExtOrIntegerDefault('compileSdkVersion')

  // Used to override the NDK path/version on internal CI or by allowing
  // users to customize the NDK path/version from their root project (e.g. for M1 support)
  if (rootProject.hasProperty("ndkPath")) {
    ndkPath rootProject.ext.ndkPath
  }
  if (rootProject.hasProperty("ndkVersion")) {
    ndkVersion rootProject.ext.ndkVersion
  }

  defaultConfig {
    minSdkVersion getExtOrIntegerDefault('minSdkVersion')
    targetSdkVersion getExtOrIntegerDefault('targetSdkVersion')
    buildConfigField "boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString()
  }

  sourceSets.main {
    java {
      if (isNewArchitectureEnabled()) {
        srcDirs += 'src/newArch/java'
      } else {
        srcDirs += 'src/oldArch/java'
      }
    }
  }
}

repositories {
  google()
  mavenLocal()
  mavenCentral()
}

dependencies {
  //noinspection GradleDynamicVersion
  implementation "com.facebook.react:react-native:+"  // From node_modules
  implementation "com.google.android.gms:play-services-auth:${safeExtGet('googlePlayServicesAuthVersion', '21.3.0')}"
}
