import mongoose from 'mongoose';

const { Schema } = mongoose;

const moduleSchema = new Schema({
  title: { type: String, required: true },
  description: { type: String },
  courseId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Course', 
    required: true 
  },
  order: { type: Number, default: 0 },
  isPublished: { type: Boolean, default: false },
  releaseDate: { type: Date },
}, { 
  timestamps: true 
});

// Create indexes for faster queries
moduleSchema.index({ courseId: 1 });
moduleSchema.index({ order: 1 });
moduleSchema.index({ isPublished: 1 });

export const Module = mongoose.model('Module', moduleSchema);
