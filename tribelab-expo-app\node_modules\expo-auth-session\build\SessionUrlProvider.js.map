{"version": 3, "file": "SessionUrlProvider.js", "sourceRoot": "", "sources": ["../src/SessionUrlProvider.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,EAAE,EAAE,oBAAoB,EAAE,MAAM,gBAAgB,CAAC;AACjE,OAAO,KAAK,OAAO,MAAM,cAAc,CAAC;AACxC,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAE7C,MAAM,OAAO,kBAAkB;IACrB,MAAM,CAAU,QAAQ,GAAG,sBAAsB,CAAC;IAClD,MAAM,CAAU,YAAY,GAAG,mBAAmB,CAAC;IAE3D,mBAAmB,CACjB,OAAgB,EAChB,OAAuD;QAEvD,MAAM,WAAW,GAAG,kBAAkB,CAAC,yBAAyB,EAAE,CAAC;QACnE,IAAI,IAAI,GAAG,kBAAkB,CAAC,YAAY,CAAC;QAC3C,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,GAAG,CAAC,IAAI,EAAE,kBAAkB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1F,CAAC;QAED,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE;YAC7B,sGAAsG;YACtG,MAAM,EAAE,OAAO,EAAE,MAAM,IAAI,OAAO,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;YACpE,WAAW;YACX,eAAe,EAAE,OAAO,EAAE,eAAe;SAC1C,CAAC,CAAC;IACL,CAAC;IAED,WAAW,CAAC,OAAe,EAAE,SAAiB,EAAE,mBAAuC;QACrF,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;YACtD,6BAA6B;YAC7B,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,MAAM,WAAW,GAAG,IAAI,eAAe,CAAC;YACtC,OAAO;YACP,SAAS;SACV,CAAC,CAAC;QAEH,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,EAAE,mBAAmB,EAAE,CAAC,UAAU,WAAW,EAAE,CAAC;IAChF,CAAC;IAED,cAAc,CAAC,OAA2D;QACxE,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;YAC1B,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;gBAC5B,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC7E,CAAC;iBAAM,CAAC;gBACN,6BAA6B;gBAC7B,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QAED,MAAM,yBAAyB,GAC7B,OAAO,CAAC,mBAAmB,IAAI,SAAS,CAAC,UAAU,EAAE,gBAAgB,CAAC;QAExE,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAC/B,IAAI,SAAS,GAAG,EAAE,CAAC;YACnB,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,SAAS,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,IAAI,EAAE,CAAC;oBACjE,SAAS;wBACP,sNAAsN,CAAC;gBAC3N,CAAC;qBAAM,IAAI,SAAS,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,WAAW,EAAE,CAAC;oBAC/E,SAAS;wBACP,6GAA6G,CAAC;gBAClH,CAAC;YACH,CAAC;YAED,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;gBACxB,SAAS;oBACP,+FAA+F;wBAC/F,6HAA6H,CAAC;YAClI,CAAC;YAED,MAAM,IAAI,KAAK,CACb,gFAAgF,GAAG,SAAS,CAC7F,CAAC;QACJ,CAAC;QAED,MAAM,WAAW,GAAG,GAAG,kBAAkB,CAAC,QAAQ,IAAI,yBAAyB,EAAE,CAAC;QAClF,IAAI,OAAO,EAAE,CAAC;YACZ,kBAAkB,CAAC,eAAe,CAAC,yBAAyB,EAAE,WAAW,CAAC,CAAC;YAC3E,oEAAoE;QACtE,CAAC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,MAAM,CAAC,yBAAyB;QACtC,IAAI,OAAO,GAAuB,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;QAChE,IACE,CAAC,OAAO;YACR,CAAC,oBAAoB,CAAC,WAAW,KAAK,SAAS,CAAC,oBAAoB;gBAClE,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,EAC5B,CAAC;YACD,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;gBAC1B,OAAO,GAAG,EAAE,CAAC;YACf,CAAC;iBAAM,CAAC;gBACN,mEAAmE;gBACnE,gFAAgF;gBAChF,OAAO,GAAG,kBAAkB,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;YAC9F,CAAC;QACH,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,QAAQ,GAAG,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,CAAC;YACH,OAAO,MAAM,CAAC,WAAW;YACvB,wEAAwE;YACxE,IAAI,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CACnC,CAAC;QACJ,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;QAEV,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,MAAM,CAAC,eAAe,CAAC,EAAU,EAAE,GAAW;QACpD,IAAI,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;YACjC,OAAO,CAAC,IAAI,CACV,+HAA+H,GAAG,6TAA6T,CAChc,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,YAAY,CAAC,GAAW;QACrC,OAAO,GAAG,CAAC,OAAO,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;IAClD,CAAC;IAEO,MAAM,CAAC,kBAAkB,CAAC,GAAW;QAC3C,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAChC,CAAC;;AAGH,eAAe,IAAI,kBAAkB,EAAE,CAAC", "sourcesContent": ["import Constants, { ExecutionEnvironment } from 'expo-constants';\nimport * as Linking from 'expo-linking';\nimport { Platform } from 'expo-modules-core';\n\nexport class SessionUrlProvider {\n  private static readonly BASE_URL = `https://auth.expo.io`;\n  private static readonly SESSION_PATH = 'expo-auth-session';\n\n  getDefaultReturnUrl(\n    urlPath?: string,\n    options?: Omit<Linking.CreateURLOptions, 'queryParams'>\n  ): string {\n    const queryParams = SessionUrlProvider.getHostAddressQueryParams();\n    let path = SessionUrlProvider.SESSION_PATH;\n    if (urlPath) {\n      path = [path, SessionUrlProvider.removeLeadingSlash(urlPath)].filter(Boolean).join('/');\n    }\n\n    return Linking.createURL(path, {\n      // The redirect URL doesn't matter for the proxy as long as it's valid, so silence warnings if needed.\n      scheme: options?.scheme ?? Linking.resolveScheme({ isSilent: true }),\n      queryParams,\n      isTripleSlashed: options?.isTripleSlashed,\n    });\n  }\n\n  getStartUrl(authUrl: string, returnUrl: string, projectNameForProxy: string | undefined): string {\n    if (Platform.OS === 'web' && !Platform.isDOMAvailable) {\n      // Return nothing in SSR envs\n      return '';\n    }\n    const queryString = new URLSearchParams({\n      authUrl,\n      returnUrl,\n    });\n\n    return `${this.getRedirectUrl({ projectNameForProxy })}/start?${queryString}`;\n  }\n\n  getRedirectUrl(options: { projectNameForProxy?: string; urlPath?: string }): string {\n    if (Platform.OS === 'web') {\n      if (Platform.isDOMAvailable) {\n        return [window.location.origin, options.urlPath].filter(Boolean).join('/');\n      } else {\n        // Return nothing in SSR envs\n        return '';\n      }\n    }\n\n    const legacyExpoProjectFullName =\n      options.projectNameForProxy || Constants.expoConfig?.originalFullName;\n\n    if (!legacyExpoProjectFullName) {\n      let nextSteps = '';\n      if (__DEV__) {\n        if (Constants.executionEnvironment === ExecutionEnvironment.Bare) {\n          nextSteps =\n            ' Ensure you have the latest version of expo-constants installed and recompile your native app. You can verify that originalFullName is defined by running `npx expo config --type public` and inspecting the output.';\n        } else if (Constants.executionEnvironment === ExecutionEnvironment.StoreClient) {\n          nextSteps =\n            ' Report this as a bug with the contents of `expo config --type public`: https://github.com/expo/expo/issues';\n        }\n      }\n\n      if (Constants.manifest2) {\n        nextSteps =\n          ' Prefer AuthRequest in combination with an Expo Development Client build of your application.' +\n          ' To continue using the AuthSession proxy, specify the project full name (@owner/slug) using the projectNameForProxy option.';\n      }\n\n      throw new Error(\n        'Cannot use the AuthSession proxy because the project full name is not defined.' + nextSteps\n      );\n    }\n\n    const redirectUrl = `${SessionUrlProvider.BASE_URL}/${legacyExpoProjectFullName}`;\n    if (__DEV__) {\n      SessionUrlProvider.warnIfAnonymous(legacyExpoProjectFullName, redirectUrl);\n      // TODO: Verify with the dev server that the manifest is up to date.\n    }\n    return redirectUrl;\n  }\n\n  private static getHostAddressQueryParams(): Record<string, string> | undefined {\n    let hostUri: string | undefined = Constants.expoConfig?.hostUri;\n    if (\n      !hostUri &&\n      (ExecutionEnvironment.StoreClient === Constants.executionEnvironment ||\n        Linking.resolveScheme({}))\n    ) {\n      if (!Constants.linkingUri) {\n        hostUri = '';\n      } else {\n        // we're probably not using up-to-date xdl, so just fake it for now\n        // we have to remove the /--/ on the end since this will be inserted again later\n        hostUri = SessionUrlProvider.removeScheme(Constants.linkingUri).replace(/\\/--(\\/.*)?$/, '');\n      }\n    }\n\n    if (!hostUri) {\n      return undefined;\n    }\n\n    const uriParts = hostUri?.split('?');\n    try {\n      return Object.fromEntries(\n        // @ts-ignore: [Symbol.iterator] is indeed, available on every platform.\n        new URLSearchParams(uriParts?.[1])\n      );\n    } catch {}\n\n    return undefined;\n  }\n\n  private static warnIfAnonymous(id: string, url: string): void {\n    if (id.startsWith('@anonymous/')) {\n      console.warn(\n        `You are not currently signed in to Expo on your development machine. As a result, the redirect URL for AuthSession will be \"${url}\". If you are using an OAuth provider that requires adding redirect URLs to an allow list, we recommend that you do not add this URL -- instead, you should sign in to Expo to acquire a unique redirect URL. Additionally, if you do decide to publish this app using Expo, you will need to register an account to do it.`\n      );\n    }\n  }\n\n  private static removeScheme(url: string) {\n    return url.replace(/^[a-zA-Z0-9+.-]+:\\/\\//, '');\n  }\n\n  private static removeLeadingSlash(url: string) {\n    return url.replace(/^\\//, '');\n  }\n}\n\nexport default new SessionUrlProvider();\n"]}