{"version": 3, "file": "ProviderUtils.js", "sourceRoot": "", "sources": ["../../src/providers/ProviderUtils.ts"], "names": [], "mappings": "AAAA,MAAM,UAAU,mBAAmB,CAAC,SAAmB,EAAE,EAAE,cAAwB;IACjF,sDAAsD;IACtD,oBAAoB;IACpB,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,MAAc,EAAE,KAAU,EAAE,YAAoB;IAChF,IAAI,OAAO,KAAK,KAAK,WAAW;QAC9B,8BAA8B;QAC9B,MAAM,IAAI,KAAK,CACb,wBAAwB,MAAM,6BAA6B,YAAY,yBAAyB,CACjG,CAAC;AACN,CAAC", "sourcesContent": ["export function applyRequiredScopes(scopes: string[] = [], requiredScopes: string[]): string[] {\n  // Add the required scopes for returning profile data.\n  // Remove duplicates\n  return [...new Set([...scopes, ...requiredScopes])];\n}\n\nexport function invariantClientId(idName: string, value: any, providerName: string): asserts value {\n  if (typeof value === 'undefined')\n    // TODO(Bacon): Add learn more\n    throw new Error(\n      `Client Id property \\`${idName}\\` must be defined to use ${providerName} auth on this platform.`\n    );\n}\n"]}