import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const url = request.nextUrl.searchParams.get("url");

    if (!url) {
      return new NextResponse("URL parameter is required", { status: 400 });
    }

    // Validate that the URL is from our R2 bucket
    const r2Domains = [
      "pub-895f71ea78c843b59c97073ccfe523c5.r2.dev",
      "pub-fa626969086b44f788fa6d3ad94f6b2f.r2.dev",
      "pub-thetribelab.r2.dev",
    ];

    const urlObj = new URL(url);
    const isValidR2Domain = r2Domains.some(
      (domain) => urlObj.hostname === domain
    );

    if (!isValidR2Domain) {
      return new NextResponse("Invalid domain", { status: 403 });
    }

    // Fetch the image from R2
    const response = await fetch(url, {
      headers: {
        "User-Agent": "TheTribelab-ImageProxy/1.0",
      },
    });

    if (!response.ok) {
      return new NextResponse("Failed to fetch image", {
        status: response.status,
      });
    }

    // Get the image data
    const imageData = await response.arrayBuffer();
    const contentType = response.headers.get("content-type") || "image/jpeg";

    // Return the image with proper CORS headers
    return new NextResponse(imageData, {
      headers: {
        "Content-Type": contentType,
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET",
        "Access-Control-Allow-Headers": "Content-Type",
        "Cache-Control": "public, max-age=31536000, immutable",
        "Cross-Origin-Resource-Policy": "cross-origin",
      },
    });
  } catch (error) {
    console.error("Image proxy error:", error);
    return new NextResponse("Internal server error", { status: 500 });
  }
}
