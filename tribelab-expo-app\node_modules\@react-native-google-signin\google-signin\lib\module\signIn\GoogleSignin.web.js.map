{"version": 3, "names": ["statusCodes", "errorMessage", "logNotImplementedError", "console", "warn", "throwNotImplementedError", "e", "Error", "Object", "assign", "code", "PLAY_SERVICES_NOT_AVAILABLE", "signIn", "_options", "hasPlayServices", "showPlayServicesUpdateDialog", "configure", "addScopes", "signInSilently", "signOut", "revokeAccess", "hasPreviousSignIn", "getCurrentUser", "clearCachedAccessToken", "_tokenString", "getTokens", "GoogleSignin"], "sourceRoot": "../../../src", "sources": ["signIn/GoogleSignin.web.ts"], "mappings": ";;AAQA,SAASA,WAAW,QAAQ,6BAA0B;AACtD,MAAMC,YAAY,GAChB,yHAAyH,GACzH,2GAA2G;AAE7G,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;EACnCC,OAAO,CAACC,IAAI,CAACH,YAAY,CAAC;AAC5B,CAAC;AAED,SAASI,wBAAwBA,CAAA,EAAU;EACzC,MAAMC,CAAC,GAAG,IAAIC,KAAK,CAACN,YAAY,CAAC;EACjC;EACAO,MAAM,CAACC,MAAM,CAACH,CAAC,EAAE;IAAEI,IAAI,EAAEV,WAAW,CAACW;EAA4B,CAAC,CAAC;EACnE,MAAML,CAAC;AACT;AAEA,eAAeM,MAAMA,CAACC,QAAsB,GAAG,CAAC,CAAC,EAAiB;EAChER,wBAAwB,CAAC,CAAC;AAC5B;AAEA,eAAeS,eAAeA,CAC5BD,QAA+B,GAAG;EAAEE,4BAA4B,EAAE;AAAK,CAAC,EACtD;EAClBb,sBAAsB,CAAC,CAAC;EACxB,OAAO,KAAK;AACd;AAEA,SAASc,SAASA,CAACH,QAAyB,EAAQ;EAClDX,sBAAsB,CAAC,CAAC;AAC1B;AAEA,eAAee,SAASA,CAACJ,QAAyB,EAAwB;EACxEX,sBAAsB,CAAC,CAAC;EACxB,OAAO,IAAI;AACb;AAEA,eAAegB,cAAcA,CAAA,EAAkB;EAC7Cb,wBAAwB,CAAC,CAAC;AAC5B;AAEA,eAAec,OAAOA,CAAA,EAAkB;EACtCjB,sBAAsB,CAAC,CAAC;EACxB,OAAO,IAAI;AACb;AAEA,eAAekB,YAAYA,CAAA,EAAkB;EAC3ClB,sBAAsB,CAAC,CAAC;EACxB,OAAO,IAAI;AACb;AAEA,SAASmB,iBAAiBA,CAAA,EAAY;EACpCnB,sBAAsB,CAAC,CAAC;EACxB,OAAO,KAAK;AACd;AAEA,SAASoB,cAAcA,CAAA,EAAgB;EACrCpB,sBAAsB,CAAC,CAAC;EACxB,OAAO,IAAI;AACb;AAEA,eAAeqB,sBAAsBA,CAACC,YAAoB,EAAiB;EACzEtB,sBAAsB,CAAC,CAAC;EACxB,OAAO,IAAI;AACb;AAEA,eAAeuB,SAASA,CAAA,EAA+B;EACrDpB,wBAAwB,CAAC,CAAC;AAC5B;AAEA,OAAO,MAAMqB,YAAY,GAAG;EAC1BZ,eAAe;EACfE,SAAS;EACTJ,MAAM;EACNK,SAAS;EACTC,cAAc;EACdC,OAAO;EACPC,YAAY;EACZC,iBAAiB;EACjBC,cAAc;EACdC,sBAAsB;EACtBE;AACF,CAAC", "ignoreList": []}