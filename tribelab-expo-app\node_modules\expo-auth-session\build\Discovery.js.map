{"version": 3, "file": "Discovery.js", "sourceRoot": "", "sources": ["../src/Discovery.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,MAAM,WAAW,CAAC;AAGlC,OAAO,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AA0MvC;;;GAGG;AACH,MAAM,UAAU,sBAAsB,CAAC,MAAc;IACnD,OAAO,GAAG,MAAM,mCAAmC,CAAC;AACtD,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,mBAAmB,CAAC,MAAc;IACtD,MAAM,IAAI,GAAG,MAAM,YAAY,CAAmB,sBAAsB,CAAC,MAAM,CAAC,EAAE;QAChF,QAAQ,EAAE,MAAM;QAChB,MAAM,EAAE,KAAK;KACd,CAAC,CAAC;IAEH,OAAO;QACL,iBAAiB,EAAE,IAAI;QACvB,qBAAqB,EAAE,IAAI,CAAC,sBAAsB;QAClD,aAAa,EAAE,IAAI,CAAC,cAAc;QAClC,kBAAkB,EAAE,IAAI,CAAC,mBAAmB;QAC5C,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;QACxC,kBAAkB,EAAE,IAAI,CAAC,oBAAoB;QAC7C,oBAAoB,EAAE,IAAI,CAAC,qBAAqB;KACjD,CAAC;AACJ,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,iBAAoC;IAEpC,SAAS,CACP,iBAAiB,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,OAAO,iBAAiB,CAAC,EAC9E,iDAAiD,CAClD,CAAC;IACF,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE,CAAC;QAC1C,OAAO,MAAM,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;IACtD,CAAC;IACD,OAAO,iBAAiB,CAAC;AAC3B,CAAC", "sourcesContent": ["import invariant from 'invariant';\n\nimport { CodeChallengeMethod } from './AuthRequest.types';\nimport { requestAsync } from './Fetch';\n\n// @needsAudit\n/**\n * URL using the `https` scheme with no query or fragment component that the OP asserts as its Issuer Identifier.\n */\nexport type Issuer = string;\n\n// @needsAudit @docsMissing\nexport type ProviderMetadataEndpoints = {\n  issuer?: Issuer;\n  /**\n   * URL of the OP's OAuth 2.0 Authorization Endpoint.\n   */\n  authorization_endpoint: string;\n  /**\n   * URL of the OP's OAuth 2.0 Token Endpoint.\n   * This is required unless only the Implicit Flow is used.\n   */\n  token_endpoint: string;\n  /**\n   * URL of the OP's UserInfo Endpoint.\n   */\n  userinfo_endpoint?: string;\n  revocation_endpoint?: string;\n  registration_endpoint?: string;\n  end_session_endpoint?: string;\n  introspection_endpoint?: string;\n  device_authorization_endpoint?: string;\n};\n\n/**\n * OpenID Providers have metadata describing their configuration.\n * [ProviderMetadata](https://openid.net/specs/openid-connect-discovery-1_0.html#ProviderMetadata)\n */\nexport type ProviderMetadata = Record<string, string | boolean | string[]> &\n  ProviderMetadataEndpoints & {\n    /**\n     * URL of the OP's JSON Web Key Set [JWK](https://openid.net/specs/openid-connect-discovery-1_0.html#JWK) document.\n     */\n    jwks_uri?: string;\n    /**\n     * JSON array containing a list of the OAuth 2.0 [RFC6749](https://openid.net/specs/openid-connect-discovery-1_0.html#RFC6749)\n     * scope values that this server supports.\n     */\n    scopes_supported?: string[];\n    /**\n     * JSON array containing a list of the OAuth 2.0 `response_type` values that this OP supports.\n     * Dynamic OpenID Providers must support the `code`, `id_token`, and the `token` `id_token` Response Type values\n     */\n    response_types_supported?: string[];\n    /**\n     * JSON array containing a list of the OAuth 2.0 `response_mode` values that this OP supports,\n     * as specified in [OAuth 2.0 Multiple Response Type Encoding Practices](https://openid.net/specs/openid-connect-discovery-1_0.html#OAuth.Responses).\n     * If omitted, the default for Dynamic OpenID Providers is `[\"query\", \"fragment\"]`.\n     */\n    response_modes_supported?: string[];\n    /**\n     * JSON array containing a list of the OAuth 2.0 Grant Type values that this OP supports.\n     * Dynamic OpenID Providers MUST support the authorization_code and implicit Grant Type values and MAY support other Grant Types.\n     * If omitted, the default value is [\"authorization_code\", \"implicit\"].\n     */\n    grant_types_supported?: string[];\n    /**\n     * JSON array containing a list of the JWS signing algorithms (alg values) supported by the OP for the ID Token to encode the Claims in a JWT.\n     * The algorithm RS256 MUST be included.\n     */\n    id_token_signing_alg_values_supported?: string[];\n    /**\n     * JSON array containing a list of the Subject Identifier types that this OP supports.\n     * Valid types include `pairwise` and `public`.\n     */\n    subject_types_supported?: string[];\n    /**\n     * A list of Client authentication methods supported by this Token Endpoint.\n     * If omitted, the default is `['client_secret_basic']`\n     */\n    token_endpoint_auth_methods_supported?: (\n      | 'client_secret_post'\n      | 'client_secret_basic'\n      | 'client_secret_jwt'\n      | 'private_key_jwt'\n      // Allows for extensions\n      | string\n    )[];\n    /**\n     *  a list of the `display` parameter values that the OpenID Provider supports.\n     */\n    display_values_supported?: string[];\n    /**\n     * a list of the Claim Types that the OpenID Provider supports.\n     */\n    claim_types_supported?: string[];\n    /**\n     * a list of the Claim Names of the Claims that the OpenID Provider may be able to supply values for.\n     * Note that for privacy or other reasons, this might not be an exhaustive list.\n     */\n    claims_supported?: string[];\n    /**\n     * URL of a page containing human-readable information that developers might want or need to know when using the OpenID Provider.\n     * In particular, if the OpenID Provider does not support Dynamic Client Registration, then information on how to register Clients\n     * needs to be provided in this documentation.\n     */\n    service_documentation?: string;\n    /**\n     * Languages and scripts supported for values in Claims being returned.\n     */\n    claims_locales_supported?: string[];\n    /**\n     * Languages and scripts supported for the user interface,\n     * represented as a JSON array of [BCP47](https://openid.net/specs/openid-connect-discovery-1_0.html#RFC5646) language tag values.\n     */\n    ui_locales_supported?: string[];\n    /**\n     * Boolean value specifying whether the OP supports use of the claims parameter, with `true` indicating support.\n     * @default false\n     */\n    claims_parameter_supported?: boolean;\n    /**\n     * Boolean value specifying whether the OP supports use of the request parameter, with `true` indicating support.\n     * @default false\n     */\n    request_parameter_supported?: boolean;\n    /**\n     * Whether the OP supports use of the `request_uri` parameter, with `true` indicating support.\n     * @default true\n     */\n    request_uri_parameter_supported?: boolean;\n    /**\n     * Whether the OP requires any `request_uri` values used to be pre-registered using the `request_uris` registration parameter.\n     * Pre-registration is required when the value is `true`.\n     * @default false\n     */\n    require_request_uri_registration?: boolean;\n    /**\n     * URL that the OpenID Provider provides to the person registering the Client to read about the OP's requirements on how\n     * the Relying Party can use the data provided by the OP. The registration process SHOULD display this URL to the person\n     * registering the Client if it is given.\n     */\n    op_policy_uri?: string;\n    /**\n     * URL that the OpenID Provider provides to the person registering the Client to read about OpenID Provider's terms of service.\n     * The registration process should display this URL to the person registering the Client if it is given.\n     */\n    op_tos_uri?: string;\n\n    code_challenge_methods_supported?: CodeChallengeMethod[];\n    check_session_iframe?: string;\n    backchannel_logout_supported?: boolean;\n    backchannel_logout_session_supported?: boolean;\n    frontchannel_logout_supported?: boolean;\n    frontchannel_logout_session_supported?: boolean;\n  };\n\n// @needsAudit\nexport type DiscoveryDocument = {\n  /**\n   * Used to interact with the resource owner and obtain an authorization grant.\n   *\n   * [Section 3.1](https://tools.ietf.org/html/rfc6749#section-3.1)\n   */\n  authorizationEndpoint?: string;\n  /**\n   * Used by the client to obtain an access token by presenting its authorization grant or refresh token.\n   * The token endpoint is used with every authorization grant except for the\n   * implicit grant type (since an access token is issued directly).\n   *\n   * [Section 3.2](https://tools.ietf.org/html/rfc6749#section-3.2)\n   */\n  tokenEndpoint?: string;\n  /**\n   * Used to revoke a token (generally for signing out). The spec requires a revocation endpoint,\n   * but some providers (like Spotify) do not support one.\n   *\n   * [Section 2.1](https://tools.ietf.org/html/rfc7009#section-2.1)\n   */\n  revocationEndpoint?: string;\n  /**\n   * URL of the OP's UserInfo Endpoint used to return info about the authenticated user.\n   *\n   * [UserInfo](https://openid.net/specs/openid-connect-core-1_0.html#UserInfo)\n   */\n  userInfoEndpoint?: string;\n  /**\n   * URL at the OP to which an RP can perform a redirect to request that the End-User be logged out at the OP.\n   *\n   * [OPMetadata](https://openid.net/specs/openid-connect-session-1_0-17.html#OPMetadata)\n   */\n  endSessionEndpoint?: string;\n  /**\n   * URL of the OP's [Dynamic Client Registration](https://openid.net/specs/openid-connect-discovery-1_0.html#OpenID.Registration) Endpoint.\n   */\n  registrationEndpoint?: string;\n  /**\n   * All metadata about the provider.\n   */\n  discoveryDocument?: ProviderMetadata;\n};\n\n// @docsMissing\nexport type IssuerOrDiscovery = Issuer | DiscoveryDocument;\n\n/**\n * Append the well known resources path and OpenID connect discovery document path to a URL\n * https://tools.ietf.org/html/rfc5785\n */\nexport function issuerWithWellKnownUrl(issuer: Issuer): string {\n  return `${issuer}/.well-known/openid-configuration`;\n}\n\n// @needsAudit\n/**\n * Fetch a `DiscoveryDocument` from a well-known resource provider that supports auto discovery.\n * @param issuer An `Issuer` URL to fetch from.\n * @return Returns a discovery document that can be used for authentication.\n */\nexport async function fetchDiscoveryAsync(issuer: Issuer): Promise<DiscoveryDocument> {\n  const json = await requestAsync<ProviderMetadata>(issuerWithWellKnownUrl(issuer), {\n    dataType: 'json',\n    method: 'GET',\n  });\n\n  return {\n    discoveryDocument: json,\n    authorizationEndpoint: json.authorization_endpoint,\n    tokenEndpoint: json.token_endpoint,\n    revocationEndpoint: json.revocation_endpoint,\n    userInfoEndpoint: json.userinfo_endpoint,\n    endSessionEndpoint: json.end_session_endpoint,\n    registrationEndpoint: json.registration_endpoint,\n  };\n}\n\n// @needsAudit\n/**\n * Utility method for resolving the discovery document from an issuer or object.\n *\n * @param issuerOrDiscovery\n */\nexport async function resolveDiscoveryAsync(\n  issuerOrDiscovery: IssuerOrDiscovery\n): Promise<DiscoveryDocument> {\n  invariant(\n    issuerOrDiscovery && !['number', 'boolean'].includes(typeof issuerOrDiscovery),\n    'Expected a valid discovery object or issuer URL'\n  );\n  if (typeof issuerOrDiscovery === 'string') {\n    return await fetchDiscoveryAsync(issuerOrDiscovery);\n  }\n  return issuerOrDiscovery;\n}\n"]}