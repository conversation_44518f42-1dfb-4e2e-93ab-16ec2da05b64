/**
 * Utility functions for image proxy to bypass CORS issues
 */

// R2 domains that need to be proxied
const R2_DOMAINS = [
  "pub-895f71ea78c843b59c97073ccfe523c5.r2.dev",
  "pub-fa626969086b44f788fa6d3ad94f6b2f.r2.dev",
  "pub-thetribelab.r2.dev",
];

/**
 * Check if a URL is from an R2 domain
 */
export function isR2Domain(url: string): boolean {
  if (!url) return false;

  try {
    const urlObj = new URL(url);
    return R2_DOMAINS.some((domain) => urlObj.hostname === domain);
  } catch {
    return false;
  }
}

/**
 * Generate a proxy URL for R2 images to bypass CORS
 */
export function getProxyUrl(originalUrl: string): string {
  if (!originalUrl) return "";

  // Only proxy R2 images
  if (!isR2Domain(originalUrl)) {
    return originalUrl;
  }

  // Generate proxy URL
  const proxyUrl = `/api/image-proxy?url=${encodeURIComponent(originalUrl)}`;
  return proxyUrl;
}

/**
 * Get the appropriate image URL - either original or proxied
 */
export function getImageUrl(url: string, useProxy: boolean = true): string {
  if (!url) return "";

  if (useProxy && isR2Domain(url)) {
    return getProxyUrl(url);
  }

  return url;
}
