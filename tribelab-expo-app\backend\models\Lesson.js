import mongoose from 'mongoose';

const { Schema } = mongoose;

const lessonSchema = new Schema({
  title: { type: String, required: true },
  description: { type: String },
  type: { 
    type: String, 
    enum: ['video', 'text', 'quiz', 'assignment'],
    default: 'text'
  },
  content: { type: String },
  videoUrl: { type: String },
  duration: { type: Number, default: 0 }, // in minutes
  order: { type: Number, default: 0 },
  moduleId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Module', 
    required: true 
  },
  courseId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Course', 
    required: true 
  },
  isPublished: { type: Boolean, default: false },
  
  // Attachments and resources
  attachments: [{
    name: { type: String, required: true },
    url: { type: String, required: true },
    type: { type: String, required: true },
    size: { type: Number }
  }],
  
  // Quiz data (if type is quiz)
  quizData: {
    questions: [{
      question: { type: String },
      options: [{ type: String }],
      correctAnswer: { type: Number },
      explanation: { type: String }
    }],
    passingScore: { type: Number, default: 70 },
    timeLimit: { type: Number } // in minutes
  }
}, { 
  timestamps: true 
});

// Create indexes for faster queries
lessonSchema.index({ moduleId: 1 });
lessonSchema.index({ courseId: 1 });
lessonSchema.index({ order: 1 });
lessonSchema.index({ isPublished: 1 });
lessonSchema.index({ type: 1 });

export const Lesson = mongoose.model('Lesson', lessonSchema);
