import mongoose from 'mongoose';
import slugify from 'slugify';

const { Schema } = mongoose;

const communitySchema = new Schema({
  name: { type: String, required: true },
  description: { type: String },
  slug: { type: String, unique: true, sparse: true },
  createdBy: { type: String, ref: 'User', required: true },
  createdAt: { type: Date, default: Date.now },
  bannerImageurl: { type: String },
  iconImageUrl: { type: String },
  aboutMedia: [{
    url: { type: String, required: true },
    type: { type: String, enum: ['image', 'video'], required: true },
    title: { type: String },
    createdAt: { type: Date, default: Date.now },
  }],
  admin: { type: String, ref: 'User', required: true },
  members: [{ type: String, ref: 'User' }],
  subAdmins: [{ type: String }],
  joinRequests: [{
    userId: { type: String, ref: 'User', required: true },
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected'],
      default: 'pending',
    },
    answers: [{ type: String }],
    createdAt: { type: Date, default: Date.now },
  }],
  adminQuestions: [{ type: String }],

  // Access settings fields
  isPrivate: { type: Boolean, default: true },
  price: { type: Number, default: 0 },
  currency: {
    type: String,
    enum: ['USD', 'INR', 'EUR', 'GBP'],
    default: 'USD',
  },
  pricingType: {
    type: String,
    enum: ['monthly', 'yearly', 'one_time'],
    default: 'one_time',
  },

  // Subscription-only fields for community management
  subscriptionPlanId: {
    type: Schema.Types.ObjectId,
    ref: 'CommunitySubscriptionPlan',
    required: false
  },
  subscriptionStatus: {
    type: String,
    enum: ['trial', 'active', 'past_due', 'cancelled', 'expired'],
    default: 'trial'
  },
  subscriptionId: { type: String },
  subscriptionStartDate: { type: Date, default: Date.now },
  subscriptionEndDate: { type: Date },
  trialEndDate: { type: Date },
  paymentDate: { type: Date },
  transactionId: { type: String },
  paymentId: { type: String },
  paymentGatewayId: {
    type: Schema.Types.ObjectId,
    ref: 'PaymentGateway',
    required: false,
  },
  freeTrialActivated: { type: Boolean, default: false },
  freeTrialStartDate: { type: Date },
  freeTrialEndDate: { type: Date },
  
  // Admin-specific trial information
  adminTrialInfo: {
    activated: { type: Boolean, default: false },
    startDate: { type: Date },
    endDate: { type: Date },
    hasUsedTrial: { type: Boolean, default: false },
    trialUsedAt: { type: Date },
    cancelled: { type: Boolean, default: false },
    cancelledDate: { type: Date }
  },

  // Community suspension status
  suspended: { type: Boolean, default: false },
  suspendedAt: { type: Date },
  suspensionReason: { type: String }
});

// Function to generate a slug from a name
function generateSlug(name) {
  return slugify(name, { lower: true, strict: true });
}

// Pre-save middleware to generate slug
communitySchema.pre('save', async function (next) {
  if (this.isModified('name') && !this.slug) {
    let baseSlug = generateSlug(this.name);
    let slug = baseSlug;
    let counter = 1;

    // Check for existing slugs and append number if needed
    while (await mongoose.model('Community').findOne({ slug, _id: { $ne: this._id } })) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    this.slug = slug;
  }
  next();
});

// Indexes for better performance
communitySchema.index({ slug: 1 });
communitySchema.index({ admin: 1 });
communitySchema.index({ members: 1 });
communitySchema.index({ createdBy: 1 });
communitySchema.index({ subscriptionStatus: 1 });

export const Community = mongoose.model('Community', communitySchema);
