{"version": 3, "names": ["NativeModule", "SIGN_IN_CANCELLED", "IN_PROGRESS", "PLAY_SERVICES_NOT_AVAILABLE", "SIGN_IN_REQUIRED", "SCOPES_ALREADY_GRANTED", "getConstants", "SIGN_IN_REQUIRED_CODE", "SIGN_IN_CANCELLED_CODE", "ios_only_SCOPES_ALREADY_GRANTED", "statusCodes", "Object", "freeze"], "sourceRoot": "../../../src", "sources": ["errors/errorCodes.ts"], "mappings": ";;AAAA,SAASA,YAAY,QAAQ,+BAA4B;AAEzD,MAAM;EACJC,iBAAiB;EACjBC,WAAW;EACXC,2BAA2B;EAC3BC,gBAAgB;EAChBC;AACF,CAAC,GAAGL,YAAY,CAACM,YAAY,CAAC,CAAC;AAE/B,OAAO,MAAMC,qBAAqB,GAAGH,gBAAgB;AACrD,OAAO,MAAMI,sBAAsB,GAAGP,iBAAiB;AACvD,OAAO,MAAMQ,+BAA+B,GAAGJ,sBAAsB;;AAErE;AACA;AACA;AACA,OAAO,MAAMK,WAAW,GAAGC,MAAM,CAACC,MAAM,CAAC;EACvCX,iBAAiB;EACjBC,WAAW;EACXC,2BAA2B;EAC3BC;AACF,CAAC,CAAC", "ignoreList": []}