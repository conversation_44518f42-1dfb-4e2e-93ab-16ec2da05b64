import mongoose from "mongoose";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import slugify from "slugify";

const { Schema } = mongoose;

const userSchema = new Schema(
  {
    _id: { type: Schema.Types.ObjectId, auto: true },
    username: { type: String, required: true, unique: true },
    name: { type: String },
    email: { type: String, required: true, unique: true },
    password: { type: String },
    provider: { type: String },
    providerId: { type: String },
    providerType: { type: String },
    slug: { type: String, unique: true, sparse: true },
    createdAt: { type: Date, default: Date.now },
    firstName: { type: String },
    lastName: { type: String },
    bio: { type: String },
    timezone: { type: String },
    notificationSettings: {
      email: { type: Boolean, default: true },
      push: { type: Boolean, default: true },
      sms: { type: Boolean, default: false },
    },
    messagingPreferences: {
      allowDirectMessages: { type: Boolean, default: true },
      blockedCommunities: [{ type: String }],
    },
    paymentSettings: {
      razorpayCustomerId: { type: String },
      subscriptionStatus: { type: String },
      subscriptionEndDate: { type: Date },
    },
    communityAdminSubscription: {
      planId: { type: Schema.Types.ObjectId, ref: "CommunitySubscriptionPlan" },
      status: {
        type: String,
        enum: ["trial", "active", "past_due", "cancelled", "expired"],
        default: "trial",
      },
      subscriptionId: { type: String },
      startDate: { type: Date, default: Date.now },
      endDate: { type: Date },
      trialEndDate: { type: Date },
      autoRenew: { type: Boolean, default: true },
      totalFailedPayments: { type: Number, default: 0 },
      lastPaymentFailure: { type: Date },
      notificationPreferences: {
        renewalReminders: { type: Boolean, default: true },
        paymentFailures: { type: Boolean, default: true },
        trialExpiry: { type: Boolean, default: true },
      },
    },
    community: [{ type: Schema.Types.ObjectId, ref: "Community" }],
    followedBy: [{ type: Schema.Types.ObjectId, ref: "User" }],
    following: [{ type: Schema.Types.ObjectId, ref: "User" }],
    profileImage: { type: String },
    emailVerified: { type: Boolean, default: false },
    verificationToken: { type: String },
    verificationTokenExpiry: { type: Date },
    role: {
      type: String,
      enum: ["user", "admin", "platform_admin"],
      default: "user",
    },
    // Gamification fields
    points: { type: Number, default: 0 },
    level: { type: Number, default: 1 },
    monthlyPoints: { type: Number, default: 0 },
    lastPointsReset: { type: Date, default: Date.now },
  },
  {
    timestamps: true,
  }
);

// Pre-save middleware for slug generation
userSchema.pre("save", async function (next) {
  if (this.isModified("username")) {
    this.slug = slugify(this.username, { lower: true, strict: true });
  }
  next();
});

// Pre-save middleware for password hashing
userSchema.pre("save", async function (next) {
  if (this.isModified("password") && this.password) {
    if (!this.password.startsWith("$2b$")) {
      this.password = await bcrypt.hash(this.password, 10);
    }
  }
  next();
});

// Instance method to check password
userSchema.methods.matchPassword = async function (enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

// Instance method to generate auth token
userSchema.methods.getSignedJwtToken = function () {
  return jwt.sign({ id: this._id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN,
  });
};

// Indexes
userSchema.index(
  { providerId: 1, providerType: 1 },
  { unique: true, sparse: true }
);
userSchema.index({ email: 1 });
userSchema.index({ username: 1 });
userSchema.index({ slug: 1 });

export const User = mongoose.model("User", userSchema);
