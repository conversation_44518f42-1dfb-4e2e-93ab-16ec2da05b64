import mongoose from 'mongoose';

const { Schema } = mongoose;

const messageSchema = new Schema({
  content: { type: String, required: true },
  sender: { 
    type: String, 
    ref: 'User', 
    required: true 
  },
  communityId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Community', 
    required: true 
  },
  type: { 
    type: String, 
    enum: ['text', 'image', 'video', 'file', 'audio'],
    default: 'text'
  },
  
  // File/media information
  media: {
    url: { type: String },
    filename: { type: String },
    size: { type: Number },
    mimeType: { type: String }
  },
  
  // Message status
  isEdited: { type: Boolean, default: false },
  editedAt: { type: Date },
  isDeleted: { type: Boolean, default: false },
  deletedAt: { type: Date },
  
  // Reactions
  reactions: [{
    userId: { type: String, ref: 'User' },
    emoji: { type: String },
    createdAt: { type: Date, default: Date.now }
  }],
  
  // Thread/reply functionality
  parentMessageId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Message' 
  },
  replyCount: { type: Number, default: 0 },
  
  // Read receipts
  readBy: [{
    userId: { type: String, ref: 'User' },
    readAt: { type: Date, default: Date.now }
  }]
}, { 
  timestamps: true 
});

// Create indexes for faster queries
messageSchema.index({ communityId: 1, createdAt: -1 });
messageSchema.index({ sender: 1 });
messageSchema.index({ parentMessageId: 1 });
messageSchema.index({ isDeleted: 1 });

export const Message = mongoose.model('Message', messageSchema);
