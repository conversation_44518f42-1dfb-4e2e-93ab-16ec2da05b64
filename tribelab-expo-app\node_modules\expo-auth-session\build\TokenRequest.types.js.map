{"version": 3, "file": "TokenRequest.types.js", "sourceRoot": "", "sources": ["../src/TokenRequest.types.ts"], "names": [], "mappings": "AAQA,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAN,IAAY,aAaX;AAbD,WAAY,aAAa;IACvB;;;;OAIG;IACH,6CAA4B,CAAA;IAC5B;;;;OAIG;IACH,+CAA8B,CAAA;AAChC,CAAC,EAbW,aAAa,KAAb,aAAa,QAaxB;AAyFD,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAN,IAAY,SAyBX;AAzBD,WAAY,SAAS;IACnB;;;;OAIG;IACH,qDAAwC,CAAA;IACxC;;;;OAIG;IACH,kCAAqB,CAAA;IACrB;;;;OAIG;IACH,2CAA8B,CAAA;IAC9B;;;;OAIG;IACH,qDAAwC,CAAA;AAC1C,CAAC,EAzBW,SAAS,KAAT,SAAS,QAyBpB", "sourcesContent": ["// @needsAudit\n/**\n * Access token type.\n *\n * @see [Section 7.1](https://tools.ietf.org/html/rfc6749#section-7.1)\n */\nexport type TokenType = 'bearer' | 'mac';\n\n// @needsAudit\n/**\n * A hint about the type of the token submitted for revocation. If not included then the server should attempt to deduce the token type.\n *\n * @see [Section 2.1](https://tools.ietf.org/html/rfc7009#section-2.1)\n */\nexport enum TokenTypeHint {\n  /**\n   * Access token.\n   *\n   * [Section 1.4](https://tools.ietf.org/html/rfc6749#section-1.4)\n   */\n  AccessToken = 'access_token',\n  /**\n   * Refresh token.\n   *\n   * [Section 1.5](https://tools.ietf.org/html/rfc6749#section-1.5)\n   */\n  RefreshToken = 'refresh_token',\n}\n\n// @needsAudit\n/**\n * Config used to request a token refresh, revocation, or code exchange.\n */\nexport type TokenRequestConfig = {\n  /**\n   * A unique string representing the registration information provided by the client.\n   * The client identifier is not a secret; it is exposed to the resource owner and shouldn't be used\n   * alone for client authentication.\n   *\n   * The client identifier is unique to the authorization server.\n   *\n   * [Section 2.2](https://tools.ietf.org/html/rfc6749#section-2.2)\n   */\n  clientId: string;\n  /**\n   * Client secret supplied by an auth provider.\n   * There is no secure way to store this on the client.\n   *\n   * [Section 2.3.1](https://tools.ietf.org/html/rfc6749#section-2.3.1)\n   */\n  clientSecret?: string;\n  /**\n   * Extra query params that'll be added to the query string.\n   */\n  extraParams?: Record<string, string>;\n  /**\n   * List of strings to request access to.\n   *\n   * [Section 3.3](https://tools.ietf.org/html/rfc6749#section-3.3)\n   */\n  scopes?: string[];\n};\n\n// @needsAudit\n/**\n * Config used to exchange an authorization code for an access token.\n *\n * @see [Section 4.1.3](https://tools.ietf.org/html/rfc6749#section-4.1.3)\n */\nexport type AccessTokenRequestConfig = TokenRequestConfig & {\n  /**\n   * The authorization code received from the authorization server.\n   */\n  code: string;\n  /**\n   * If the `redirectUri` parameter was included in the `AuthRequest`, then it must be supplied here as well.\n   *\n   * [Section 3.1.2](https://tools.ietf.org/html/rfc6749#section-3.1.2)\n   */\n  redirectUri: string;\n};\n\n// @needsAudit\n/**\n * Config used to request a token refresh, or code exchange.\n *\n * @see [Section 6](https://tools.ietf.org/html/rfc6749#section-6)\n */\nexport type RefreshTokenRequestConfig = TokenRequestConfig & {\n  /**\n   * The refresh token issued to the client.\n   */\n  refreshToken?: string;\n};\n\n// @needsAudit\n/**\n * Config used to revoke a token.\n *\n * @see [Section 2.1](https://tools.ietf.org/html/rfc7009#section-2.1)\n */\nexport type RevokeTokenRequestConfig = Partial<TokenRequestConfig> & {\n  /**\n   * The token that the client wants to get revoked.\n   *\n   * [Section 3.1](https://tools.ietf.org/html/rfc6749#section-3.1)\n   */\n  token: string;\n  /**\n   * A hint about the type of the token submitted for revocation.\n   *\n   * [Section 3.2](https://tools.ietf.org/html/rfc6749#section-3.2)\n   */\n  tokenTypeHint?: TokenTypeHint;\n};\n\n// @needsAudit\n/**\n * Grant type values used in dynamic client registration and auth requests.\n *\n * @see [Appendix A.10](https://tools.ietf.org/html/rfc6749#appendix-A.10)\n */\nexport enum GrantType {\n  /**\n   * Used for exchanging an authorization code for one or more tokens.\n   *\n   * [Section 4.1.3](https://tools.ietf.org/html/rfc6749#section-4.1.3)\n   */\n  AuthorizationCode = 'authorization_code',\n  /**\n   * Used when obtaining an access token.\n   *\n   * [Section 4.2](https://tools.ietf.org/html/rfc6749#section-4.2)\n   */\n  Implicit = 'implicit',\n  /**\n   * Used when exchanging a refresh token for a new token.\n   *\n   * [Section 6](https://tools.ietf.org/html/rfc6749#section-6)\n   */\n  RefreshToken = 'refresh_token',\n  /**\n   * Used for client credentials flow.\n   *\n   * [Section 4.4.2](https://tools.ietf.org/html/rfc6749#section-4.4.2)\n   */\n  ClientCredentials = 'client_credentials',\n}\n\n// @needsAudit @docsMissing\n/**\n * Object returned from the server after a token response.\n */\nexport type ServerTokenResponseConfig = {\n  access_token: string;\n  token_type?: TokenType;\n  expires_in?: number;\n  refresh_token?: string;\n  scope?: string;\n  id_token?: string;\n  issued_at?: number;\n};\n\n// @needsAudit\nexport type TokenResponseConfig = {\n  /**\n   * The access token issued by the authorization server.\n   *\n   * [Section 4.2.2](https://tools.ietf.org/html/rfc6749#section-4.2.2)\n   */\n  accessToken: string;\n  /**\n   * The type of the token issued. Value is case insensitive.\n   *\n   * [Section 7.1](https://tools.ietf.org/html/rfc6749#section-7.1)\n   */\n  tokenType?: TokenType;\n  /**\n   * The lifetime in seconds of the access token.\n   *\n   * For example, the value `3600` denotes that the access token will\n   * expire in one hour from the time the response was generated.\n   *\n   * If omitted, the authorization server should provide the\n   * expiration time via other means or document the default value.\n   *\n   * [Section 4.2.2](https://tools.ietf.org/html/rfc6749#section-4.2.2)\n   */\n  expiresIn?: number;\n  /**\n   * The refresh token, which can be used to obtain new access tokens using the same authorization grant.\n   *\n   * [Section 5.1](https://tools.ietf.org/html/rfc6749#section-5.1)\n   */\n  refreshToken?: string;\n  /**\n   * The scope of the access token. Only required if it's different to the scope that was requested by the client.\n   *\n   * [Section 3.3](https://tools.ietf.org/html/rfc6749#section-3.3)\n   */\n  scope?: string;\n  /**\n   * Required if the \"state\" parameter was present in the client\n   * authorization request.  The exact value received from the client.\n   *\n   * [Section 4.2.2](https://tools.ietf.org/html/rfc6749#section-4.2.2)\n   */\n  state?: string;\n  /**\n   * ID Token value associated with the authenticated session.\n   *\n   * [TokenResponse](https://openid.net/specs/openid-connect-core-1_0.html#TokenResponse)\n   */\n  idToken?: string;\n  /**\n   * Time in seconds when the token was received by the client.\n   */\n  issuedAt?: number;\n};\n"]}