{"version": 3, "file": "AuthRequestHooks.js", "sourceRoot": "", "sources": ["../src/AuthRequestHooks.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AAElE,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAG5C,OAAO,EAAwC,qBAAqB,EAAE,MAAM,aAAa,CAAC;AAE1F,cAAc;AACd;;;;;;;;;;;GAWG;AACH,MAAM,UAAU,gBAAgB,CAAC,iBAAoC;IACnE,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAA2B,IAAI,CAAC,CAAC;IAE3E,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,SAAS,GAAG,IAAI,CAAC;QACrB,qBAAqB,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;YAC1D,IAAI,SAAS,EAAE,CAAC;gBACd,YAAY,CAAC,SAAS,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,EAAE;YACV,SAAS,GAAG,KAAK,CAAC;QACpB,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC;IAExB,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,MAAM,UAAU,oBAAoB,CAClC,MAAyB,EACzB,SAAmC,EACnC,mBAAuC;IAEvC,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAqB,IAAI,CAAC,CAAC;IACjE,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IAC7C,MAAM,YAAY,GAAG,kBAAkB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACvD,MAAM,iBAAiB,GAAG,OAAO,CAC/B,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,IAAI,EAAE,CAAC,EAC9C,CAAC,MAAM,CAAC,WAAW,CAAC,CACrB,CAAC;IACF,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,SAAS,GAAG,IAAI,CAAC;QAErB,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,OAAO,GAAG,IAAI,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAChD,OAAO,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC5C,IAAI,SAAS,EAAE,CAAC;oBACd,UAAU,CAAC,OAAO,CAAC,CAAC;gBACtB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO,GAAG,EAAE;YACV,SAAS,GAAG,KAAK,CAAC;QACpB,CAAC,CAAC;IACJ,CAAC,EAAE;QACD,SAAS,EAAE,qBAAqB;QAChC,MAAM,CAAC,QAAQ;QACf,MAAM,CAAC,WAAW;QAClB,MAAM,CAAC,YAAY;QACnB,MAAM,CAAC,YAAY;QACnB,MAAM,CAAC,aAAa;QACpB,MAAM,CAAC,KAAK;QACZ,MAAM,CAAC,OAAO;QACd,WAAW;QACX,YAAY;QACZ,iBAAiB;KAClB,CAAC,CAAC;IACH,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CAAC,MAAqC;IAC/D,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO;IACT,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QAC1B,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAID,MAAM,UAAU,oBAAoB,CAClC,OAA2B,EAC3B,SAAmC,EACnC,gBAA0C,EAAE;IAE5C,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,QAAQ,CAA2B,IAAI,CAAC,CAAC;IAErE,MAAM,WAAW,GAAG,WAAW,CAC7B,KAAK,EAAE,EAAE,cAAc,GAAG,EAAE,EAAE,GAAG,OAAO,KAA+B,EAAE,EAAE,EAAE;QAC3E,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;QAC3F,CAAC;QACD,MAAM,YAAY,GAAG;YACnB,GAAG,aAAa;YAChB,GAAG,OAAO;YACV,cAAc,EAAE;gBACd,GAAG,CAAC,aAAa,CAAC,cAAc,IAAI,EAAE,CAAC;gBACvC,GAAG,cAAc;aAClB;SACF,CAAC;QACF,MAAM,MAAM,GAAG,MAAM,OAAO,EAAE,WAAW,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QACnE,SAAS,CAAC,MAAM,CAAC,CAAC;QAClB,OAAO,MAAM,CAAC;IAChB,CAAC,EACD,CAAC,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,qBAAqB,CAAC,CACjD,CAAC;IAEF,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAC/B,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,UAAU,cAAc,CAC5B,MAAyB,EACzB,SAAmC;IAMnC,MAAM,OAAO,GAAG,oBAAoB,CAAC,MAAM,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;IACrE,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,GAAG,oBAAoB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IACvE,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;AACxC,CAAC", "sourcesContent": ["import { use<PERSON><PERSON>back, useMemo, useEffect, useState } from 'react';\n\nimport { AuthRequest } from './AuthRequest';\nimport { AuthRequestConfig, AuthRequestPromptOptions, Prompt } from './AuthRequest.types';\nimport { AuthSessionResult } from './AuthSession.types';\nimport { DiscoveryDocument, IssuerOrDiscovery, resolveDiscoveryAsync } from './Discovery';\n\n// @needsAudit\n/**\n * Given an OpenID Connect issuer URL, this will fetch and return the [`DiscoveryDocument`](#discoverydocument)\n * (a collection of URLs) from the resource provider.\n *\n * @param issuerOrDiscovery URL using the `https` scheme with no query or fragment component that the OP asserts as its Issuer Identifier.\n * @return Returns `null` until the [`DiscoveryDocument`](#discoverydocument) has been fetched from the provided issuer URL.\n *\n * @example\n * ```ts\n * const discovery = useAutoDiscovery('https://example.com/auth');\n * ```\n */\nexport function useAutoDiscovery(issuerOrDiscovery: IssuerOrDiscovery): DiscoveryDocument | null {\n  const [discovery, setDiscovery] = useState<DiscoveryDocument | null>(null);\n\n  useEffect(() => {\n    let isAllowed = true;\n    resolveDiscoveryAsync(issuerOrDiscovery).then((discovery) => {\n      if (isAllowed) {\n        setDiscovery(discovery);\n      }\n    });\n\n    return () => {\n      isAllowed = false;\n    };\n  }, [issuerOrDiscovery]);\n\n  return discovery;\n}\n\nexport function useLoadedAuthRequest(\n  config: AuthRequestConfig,\n  discovery: DiscoveryDocument | null,\n  AuthRequestInstance: typeof AuthRequest\n): AuthRequest | null {\n  const [request, setRequest] = useState<AuthRequest | null>(null);\n  const scopeString = config.scopes?.join(' ');\n  const promptString = createPromptString(config.prompt);\n  const extraParamsString = useMemo(\n    () => JSON.stringify(config.extraParams || {}),\n    [config.extraParams]\n  );\n  useEffect(() => {\n    let isMounted = true;\n\n    if (discovery) {\n      const request = new AuthRequestInstance(config);\n      request.makeAuthUrlAsync(discovery).then(() => {\n        if (isMounted) {\n          setRequest(request);\n        }\n      });\n    }\n    return () => {\n      isMounted = false;\n    };\n  }, [\n    discovery?.authorizationEndpoint,\n    config.clientId,\n    config.redirectUri,\n    config.responseType,\n    config.clientSecret,\n    config.codeChallenge,\n    config.state,\n    config.usePKCE,\n    scopeString,\n    promptString,\n    extraParamsString,\n  ]);\n  return request;\n}\n\n/**\n * @returns Prompt type converted to a primitive value to be used as a React hook dependency\n */\nfunction createPromptString(prompt: Prompt | Prompt[] | undefined): string | undefined {\n  if (!prompt) {\n    return;\n  }\n\n  if (Array.isArray(prompt)) {\n    return prompt.join(' ');\n  }\n\n  return prompt;\n}\n\nexport type PromptMethod = (options?: AuthRequestPromptOptions) => Promise<AuthSessionResult>;\n\nexport function useAuthRequestResult(\n  request: AuthRequest | null,\n  discovery: DiscoveryDocument | null,\n  customOptions: AuthRequestPromptOptions = {}\n): [AuthSessionResult | null, PromptMethod] {\n  const [result, setResult] = useState<AuthSessionResult | null>(null);\n\n  const promptAsync = useCallback(\n    async ({ windowFeatures = {}, ...options }: AuthRequestPromptOptions = {}) => {\n      if (!discovery || !request) {\n        throw new Error('Cannot prompt to authenticate until the request has finished loading.');\n      }\n      const inputOptions = {\n        ...customOptions,\n        ...options,\n        windowFeatures: {\n          ...(customOptions.windowFeatures ?? {}),\n          ...windowFeatures,\n        },\n      };\n      const result = await request?.promptAsync(discovery, inputOptions);\n      setResult(result);\n      return result;\n    },\n    [request?.url, discovery?.authorizationEndpoint]\n  );\n\n  return [result, promptAsync];\n}\n\n// @needsAudit\n/**\n * Load an authorization request for a code. When the prompt method completes then the response will be fulfilled.\n *\n * > In order to close the popup window on web, you need to invoke `WebBrowser.maybeCompleteAuthSession()`.\n * > See the [GitHub example](/guides/authentication#github) for more info.\n *\n * If an Implicit grant flow was used, you can pass the `response.params` to `TokenResponse.fromQueryParams()`\n * to get a `TokenResponse` instance which you can use to easily refresh the token.\n *\n * @param config A valid [`AuthRequestConfig`](#authrequestconfig) that specifies what provider to use.\n * @param discovery A loaded [`DiscoveryDocument`](#discoverydocument) with endpoints used for authenticating.\n * Only `authorizationEndpoint` is required for requesting an authorization code.\n *\n * @return Returns a loaded request, a response, and a prompt method in a single array in the following order:\n * - `request` - An instance of [`AuthRequest`](#authrequest) that can be used to prompt the user for authorization.\n *   This will be `null` until the auth request has finished loading.\n * - `response` - This is `null` until `promptAsync` has been invoked. Once fulfilled it will return information about the authorization.\n * - `promptAsync` - When invoked, a web browser will open up and prompt the user for authentication.\n *   Accepts an [`AuthRequestPromptOptions`](#authrequestpromptoptions) object with options about how the prompt will execute.\n *\n * @example\n * ```ts\n * const [request, response, promptAsync] = useAuthRequest({ ... }, { ... });\n * ```\n */\nexport function useAuthRequest(\n  config: AuthRequestConfig,\n  discovery: DiscoveryDocument | null\n): [\n  AuthRequest | null,\n  AuthSessionResult | null,\n  (options?: AuthRequestPromptOptions) => Promise<AuthSessionResult>,\n] {\n  const request = useLoadedAuthRequest(config, discovery, AuthRequest);\n  const [result, promptAsync] = useAuthRequestResult(request, discovery);\n  return [request, result, promptAsync];\n}\n"]}