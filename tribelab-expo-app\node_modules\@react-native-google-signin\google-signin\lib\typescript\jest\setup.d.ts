import type { SignInResponse } from '../src';
export declare const mockUserInfo: Readonly<{
    idToken: "mockIdToken";
    serverAuthCode: "mockServerAuthCode";
    scopes: never[];
    user: {
        email: string;
        id: string;
        givenName: string;
        familyName: string;
        photo: null;
        name: string;
    };
}>;
export declare const mockGoogleSignInResponse: SignInResponse;
//# sourceMappingURL=setup.d.ts.map