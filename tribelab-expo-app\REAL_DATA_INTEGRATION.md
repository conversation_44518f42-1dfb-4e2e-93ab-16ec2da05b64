# TribeLab Real Data Integration

This document outlines the changes made to transition the TribeLab Expo/React Native app from mock data to real data sources.

## Summary of Changes

### 1. API Service Configuration ✅
- **File**: `src/services/api.ts`
- **Changes**: 
  - Disabled mock API mode by default
  - Updated API base URL to point to standalone backend (`http://localhost:4000/api`)
  - Removed mock response handling from request interceptor
  - Cleaned up response interceptor to remove mock data fallbacks

### 2. Community Screens Updated ✅
- **File**: `src/screens/CommunityDirectoryScreen.tsx`
- **Changes**:
  - Replaced hardcoded mock data with Redux store integration
  - Updated Community interface to match backend data structure
  - Added proper error handling with Toast notifications
  - Implemented loading states and empty states
  - Updated field mappings (`id` → `_id`, `avatar` → `iconImageUrl`, etc.)

- **File**: `src/screens/CommunityLeaderboardScreen.tsx`
- **Changes**:
  - Removed mock leaderboard data
  - Added placeholder for future leaderboard API implementation
  - Note: Backend doesn't have leaderboard functionality yet

### 3. Admin Screens Updated ✅
- **File**: `src/screens/AdminUserManagementScreen.tsx`
- **Changes**:
  - Integrated with Redux admin slice
  - Removed mock user data
  - Updated User interface to match backend structure
  - Added proper error handling
  - Updated field mappings for backend compatibility

### 4. Redux Store Integration ✅
- **Files**: `src/store/slices/communitySlice.ts`, `src/store/slices/adminSlice.ts`
- **Status**: Already properly configured for real API calls
- **Changes**: Removed any remaining mock data fallbacks

### 5. Environment Configuration ✅
- **Files**: `.env`, `.env.example`
- **Changes**:
  - Updated API URLs to include `/api` prefix
  - Set `ENABLE_MOCK_API=false` by default
  - Configured proper backend URLs for development and production

### 6. Error Handling & Loading States ✅
- **New Files**: 
  - `src/utils/errorHandler.ts` - Centralized error handling utilities
  - `src/components/LoadingState.tsx` - Reusable loading/error/empty state component
- **Features**:
  - Consistent error messaging with Toast notifications
  - Proper loading indicators
  - Empty state handling
  - Retry functionality

## Backend Integration Status

### ✅ Working Features
- **Authentication**: Login, register, JWT token management
- **Communities**: CRUD operations, member management
- **Users**: Profile management, user data
- **File Uploads**: Profile images, community images
- **Real-time**: WebSocket support for live updates

### ⚠️ Pending Features
- **Leaderboard**: Backend doesn't have leaderboard endpoints yet
- **Advanced Analytics**: User activity tracking needs implementation
- **Push Notifications**: Integration pending

## How to Run with Real Data

### Prerequisites
1. **MongoDB**: Ensure MongoDB is running on `localhost:27017`
2. **Node.js**: Version 16+ required for backend

### Step 1: Start the Backend
```bash
cd tribelab-expo-app/backend
npm install
npm run dev
```
The backend will start on `http://localhost:4000`

### Step 2: Start the Mobile App
```bash
cd tribelab-expo-app
npm install
npx expo start
```

### Step 3: Test Backend Connection (Optional)
```bash
cd tribelab-expo-app
node test-backend.js
```

## Data Structure Changes

### Community Object
**Before (Mock)**:
```typescript
{
  id: string;
  name: string;
  avatar: string;
  banner: string;
  // ...
}
```

**After (Real Backend)**:
```typescript
{
  _id: string;
  name: string;
  iconImageUrl?: string;
  bannerImageurl?: string;
  // ...
}
```

### User Object
**Before (Mock)**:
```typescript
{
  id: string;
  role: "admin" | "moderator" | "user";
  // ...
}
```

**After (Real Backend)**:
```typescript
{
  _id: string;
  role: "admin" | "platform_admin" | "user";
  // ...
}
```

## Testing Checklist

- [ ] Backend starts successfully on port 4000
- [ ] MongoDB connection established
- [ ] Community directory loads real data
- [ ] User authentication works
- [ ] Error handling displays proper messages
- [ ] Loading states show correctly
- [ ] Empty states display when no data
- [ ] Refresh functionality works

## Next Steps

1. **Implement Leaderboard API**: Add backend endpoints for community leaderboards
2. **Add Real-time Updates**: Integrate WebSocket for live data updates
3. **Implement Push Notifications**: Add notification system
4. **Add Data Validation**: Ensure all API responses are properly validated
5. **Performance Optimization**: Add caching and pagination improvements

## Troubleshooting

### Common Issues

1. **"Network Error"**: 
   - Ensure backend is running on port 4000
   - Check if MongoDB is running
   - Verify API_BASE_URL in .env file

2. **"No communities found"**:
   - Backend database might be empty
   - Create some test communities through the API
   - Check backend logs for errors

3. **Authentication Issues**:
   - Clear app storage/cache
   - Check JWT_SECRET in backend .env
   - Verify CORS settings in backend

### Debug Mode
Set `DEBUG_API_CALLS=true` in `.env` to see detailed API request/response logs.
