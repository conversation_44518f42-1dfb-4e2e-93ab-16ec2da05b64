{"name": "expo-auth-session", "version": "6.2.1", "description": "Expo module for browser-based authentication", "main": "build/index.js", "types": "build/index.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-auth-session", "auth", "o<PERSON>h", "authentication", "auth-session"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-auth-session"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/auth-session", "dependencies": {"expo-application": "~6.1.5", "expo-constants": "~17.1.7", "expo-crypto": "~14.1.5", "expo-linking": "~7.1.7", "expo-web-browser": "~14.2.0", "invariant": "^2.2.4"}, "devDependencies": {"expo-module-scripts": "^4.1.9"}, "peerDependencies": {"react": "*", "react-native": "*"}, "jest": {"preset": "expo-module-scripts"}, "gitHead": "1c4a89b0c0adebb53ef84b4a6ac25864e4652917"}