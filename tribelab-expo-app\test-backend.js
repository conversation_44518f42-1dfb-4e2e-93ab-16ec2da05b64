// Simple test script to verify backend connectivity
const axios = require('axios');

const API_BASE_URL = 'http://localhost:4000/api';

async function testBackendConnection() {
  console.log('🔍 Testing backend connection...');
  
  try {
    // Test basic health check
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get(`${API_BASE_URL}/health`);
    console.log('✅ Health check passed:', healthResponse.data);
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
  }

  try {
    // Test communities endpoint
    console.log('2. Testing communities endpoint...');
    const communitiesResponse = await axios.get(`${API_BASE_URL}/community`);
    console.log('✅ Communities endpoint working:', communitiesResponse.data);
  } catch (error) {
    console.log('❌ Communities endpoint failed:', error.message);
  }

  try {
    // Test auth endpoints
    console.log('3. Testing auth endpoints...');
    const authResponse = await axios.post(`${API_BASE_URL}/auth/register`, {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'testpassword123'
    });
    console.log('✅ Auth endpoint working:', authResponse.data);
  } catch (error) {
    console.log('❌ Auth endpoint failed:', error.message);
  }
}

// Run the test
testBackendConnection().then(() => {
  console.log('🏁 Backend test completed');
}).catch((error) => {
  console.error('💥 Test failed:', error.message);
});
