{"version": 3, "names": ["Platform", "NativeModule", "ios_only_SCOPES_ALREADY_GRANTED", "SIGN_IN_REQUIRED_CODE", "noSavedCredentialFoundResult", "translateCancellationError", "isErrorWithCode", "config<PERSON>rom<PERSON>", "Promise", "resolve", "configure", "options", "offlineAccess", "webClientId", "Error", "__DEV__", "console", "error", "signIn", "user", "createSuccessResponse", "err", "hasPlayServices", "OS", "process", "env", "NODE_ENV", "showPlayServicesUpdateDialog", "undefined", "playServicesAvailable", "addScopes", "code", "GoogleSignin", "getCurrentUser", "<PERSON><PERSON>ser", "signInSilently", "signOut", "revokeAccess", "hasPreviousSignIn", "clearCachedAccessToken", "tokenString", "reject", "getTokens", "userObject", "idToken", "accessToken", "data", "type"], "sourceRoot": "../../../src", "sources": ["signIn/GoogleSignin.ts"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,cAAc;AAWvC,SAASC,YAAY,QAAQ,+BAA4B;AACzD,SACEC,+BAA+B,EAC/BC,qBAAqB,QAChB,sBAAsB;AAC7B,SAASC,4BAA4B,QAAQ,iBAAc;AAC3D,SAASC,0BAA0B,QAAQ,gCAA6B;AACxE,SAASC,eAAe,QAAQ,iBAAc;AAE9C,IAAIC,aAAa,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;AAErC,SAASC,SAASA,CAACC,OAAwB,GAAG,CAAC,CAAC,EAAQ;EACtD,IAAIA,OAAO,CAACC,aAAa,IAAI,CAACD,OAAO,CAACE,WAAW,EAAE;IACjD,MAAM,IAAIC,KAAK,CAAC,0DAA0D,CAAC;EAC7E;EACA,IAAIC,OAAO,IAAI,iBAAiB,IAAIJ,OAAO,EAAE;IAC3CK,OAAO,CAACC,KAAK,CACX,6FACF,CAAC;EACH;EAEAV,aAAa,GAAGN,YAAY,CAACS,SAAS,CAACC,OAAO,CAAC;AACjD;;AAEA;AACA;AACA;AACA;AACA;;AASA;AACA;AACA;;AAGA,eAAeO,MAAMA,CAACP,OAAqB,GAAG,CAAC,CAAC,EAA2B;EACzE,MAAMJ,aAAa;EACnB,IAAI;IACF,MAAMY,IAAI,GAAI,MAAMlB,YAAY,CAACiB,MAAM,CAACP,OAAO,CAAU;IACzD,OAAOS,qBAAqB,CAACD,IAAI,CAAC;EACpC,CAAC,CAAC,OAAOE,GAAG,EAAE;IACZ,OAAOhB,0BAA0B,CAACgB,GAAG,CAAC;EACxC;AACF;AAEA,eAAeC,eAAeA,CAC5BX,OAA+B,EACb;EAClB,IAAIX,QAAQ,CAACuB,EAAE,KAAK,KAAK,EAAE;IACzB,OAAO,IAAI;EACb,CAAC,MAAM;IACL,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIf,OAAO,IAAIA,OAAO,CAACgB,4BAA4B,KAAKC,SAAS,EAAE;QACjE,MAAM,IAAId,KAAK,CACb,yGACF,CAAC;MACH;IACF;IACA,OAAOb,YAAY,CAAC4B,qBAAqB,CACvClB,OAAO,EAAEgB,4BAA4B,KAAK,KAC5C,CAAC;EACH;AACF;AAEA,eAAeG,SAASA,CACtBnB,OAAwB,EACQ;EAChC,IAAIX,QAAQ,CAACuB,EAAE,KAAK,KAAK,EAAE;IACzB,IAAI;MACF,MAAMJ,IAAI,GAAI,MAAMlB,YAAY,CAAC6B,SAAS,CAACnB,OAAO,CAAiB;MACnE,IAAI,CAACQ,IAAI,EAAE;QACT,OAAO,IAAI;MACb;MACA,OAAOC,qBAAqB,CAACD,IAAI,CAAC;IACpC,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZ,IACEf,eAAe,CAACe,GAAG,CAAC,IACpBA,GAAG,CAACU,IAAI,KAAK7B,+BAA+B,EAC5C;QACA;QACA,MAAMiB,IAAI,GAAGa,YAAY,CAACC,cAAc,CAAC,CAAC;QAC1C,IAAI,CAACd,IAAI,EAAE;UACT,OAAO,IAAI;QACb;QACA,OAAOC,qBAAqB,CAACD,IAAI,CAAC;MACpC;MACA,OAAOd,0BAA0B,CAACgB,GAAG,CAAC;IACxC;EACF,CAAC,MAAM;IACL;IACA,MAAMa,OAAO,GAAG,MAAMjC,YAAY,CAAC6B,SAAS,CAACnB,OAAO,CAAC;IACrD,IAAI,CAACuB,OAAO,EAAE;MACZ,OAAO,IAAI;IACb;IACA;IACA;IACA;IACA,OAAOC,cAAc,CAAC,CAAC;EACzB;AACF;;AAEA;AACA;AACA;AACA;;AAKA,eAAeA,cAAcA,CAAA,EAAoC;EAC/D,IAAI;IACF,MAAM5B,aAAa;IACnB,MAAMY,IAAI,GAAI,MAAMlB,YAAY,CAACkC,cAAc,CAAC,CAAU;IAC1D,OAAOf,qBAAqB,CAACD,IAAI,CAAC;EACpC,CAAC,CAAC,OAAOE,GAAG,EAAE;IACZ,IAAIf,eAAe,CAACe,GAAG,CAAC,IAAIA,GAAG,CAACU,IAAI,KAAK5B,qBAAqB,EAAE;MAC9D,OAAOC,4BAA4B;IACrC;IACA,MAAMiB,GAAG;EACX;AACF;AAEA,eAAee,OAAOA,CAAA,EAAkB;EACtC,OAAOnC,YAAY,CAACmC,OAAO,CAAC,CAAC;AAC/B;AAEA,eAAeC,YAAYA,CAAA,EAAkB;EAC3C,OAAOpC,YAAY,CAACoC,YAAY,CAAC,CAAC;AACpC;AAEA,SAASC,iBAAiBA,CAAA,EAAY;EACpC,OAAOrC,YAAY,CAACqC,iBAAiB,CAAC,CAAC;AACzC;AAEA,SAASL,cAAcA,CAAA,EAAgB;EACrC,OAAOhC,YAAY,CAACgC,cAAc,CAAC,CAAC;AACtC;AAEA,eAAeM,sBAAsBA,CAACC,WAAmB,EAAiB;EACxE,IAAI,CAACA,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;IACnD,OAAOhC,OAAO,CAACiC,MAAM,CACnB,gEACF,CAAC;EACH;EACA,OAAOzC,QAAQ,CAACuB,EAAE,KAAK,KAAK,GACxB,IAAI,GACJtB,YAAY,CAACsC,sBAAsB,CAACC,WAAW,CAAC;AACtD;AAEA,eAAeE,SAASA,CAAA,EAA+B;EACrD,IAAI1C,QAAQ,CAACuB,EAAE,KAAK,KAAK,EAAE;IACzB,OAAOtB,YAAY,CAACyC,SAAS,CAAC,CAAC;EACjC,CAAC,MAAM;IACL,MAAMC,UAAU,GAAG,MAAM1C,YAAY,CAACyC,SAAS,CAAC,CAAC;IACjD,OAAO;MACLE,OAAO,EAAED,UAAU,CAACC,OAAO;MAC3BC,WAAW,EAAEF,UAAU,CAACE;IAC1B,CAAC;EACH;AACF;AAEA,MAAMzB,qBAAqB,GAAI0B,IAAU,KAA6B;EACpEC,IAAI,EAAE,SAAS;EACfD;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,OAAO,MAAMd,YAAY,GAAG;EAC1BV,eAAe;EACfZ,SAAS;EACTQ,MAAM;EACNY,SAAS;EACTK,cAAc;EACdC,OAAO;EACPC,YAAY;EACZC,iBAAiB;EACjBL,cAAc;EACdM,sBAAsB;EACtBG;AACF,CAAC", "ignoreList": []}