{"version": 3, "names": ["React", "useCallback", "StyleSheet", "useColorScheme", "RNGoogleSigninButton", "NativeModule", "Color", "jsx", "_jsx", "BUTTON_SIZE_WIDE", "BUTTON_SIZE_ICON", "BUTTON_SIZE_STANDARD", "getConstants", "GoogleSigninButton", "onPress", "style", "color", "size", "rest", "activeColorScheme", "usedColor", "recommendedSize", "getSizeStyle", "stripOnPressParams", "compose", "nativeSizes", "Icon", "Standard", "Wide", "Size", "styles", "iconSize", "wideSize", "standardSize", "create", "width", "height"], "sourceRoot": "../../../src", "sources": ["buttons/GoogleSigninButton.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,WAAW,QAAQ,OAAO;AAE1C,SAASC,UAAU,EAAEC,cAAc,QAAmB,cAAc;AACpE,OAAOC,oBAAoB,MAAM,qCAAqC;AACtE,SAASC,YAAY,QAAQ,+BAA4B;AACzD,SAASC,KAAK,QAAQ,cAAW;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAElC,MAAM;EAAEC,gBAAgB;EAAEC,gBAAgB;EAAEC;AAAqB,CAAC,GAChEN,YAAY,CAACO,YAAY,CAAC,CAAC;;AAE7B;AACA;AACA;;AAQA;AACA;AACA;AACA,OAAO,MAAMC,kBAAkB,GAAGA,CAAC;EACjCC,OAAO;EACPC,KAAK;EACLC,KAAK;EACLC,IAAI,GAAGN,oBAAoB;EAC3B,GAAGO;AACoB,CAAC,KAAK;EAC7B,MAAMC,iBAAiB,GAAGhB,cAAc,CAAC,CAAC;EAC1C,MAAMiB,SAAS,GAAGJ,KAAK,IAAIG,iBAAiB,IAAI,OAAO;EAEvD,MAAME,eAAe,GAAGC,YAAY,CAACL,IAAI,CAAC;EAE1C,MAAMM,kBAAkB,GAAGtB,WAAW,CAAC,MAAM;IAC3C;IACA;IACAa,OAAO,GAAG,CAAC;EACb,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAEb,oBACEN,IAAA,CAACJ,oBAAoB;IAAA,GACfc,IAAI;IACRD,IAAI,EAAEA,IAAK;IACXH,OAAO,EAAES,kBAAmB;IAC5BP,KAAK,EAAEI,SAAU;IACjBL,KAAK,EAAEb,UAAU,CAACsB,OAAO,CAACH,eAAe,EAAEN,KAAK;EAAE,CACnD,CAAC;AAEN,CAAC;AAED,MAAMU,WAAW,GAAG;EAClBC,IAAI,EAAEhB,gBAAgB;EACtBiB,QAAQ,EAAEhB,oBAAoB;EAC9BiB,IAAI,EAAEnB;AACR,CAAC;AAEDI,kBAAkB,CAACgB,IAAI,GAAGJ,WAAW;AACrCZ,kBAAkB,CAACP,KAAK,GAAGA,KAAK;AAEhC,SAASgB,YAAYA,CAACL,IAAqC,EAAE;EAC3D,QAAQA,IAAI;IACV,KAAKP,gBAAgB;MACnB,OAAOoB,MAAM,CAACC,QAAQ;IACxB,KAAKtB,gBAAgB;MACnB,OAAOqB,MAAM,CAACE,QAAQ;IACxB;MACE,OAAOF,MAAM,CAACG,YAAY;EAC9B;AACF;;AAEA;AACA,MAAMH,MAAM,GAAG5B,UAAU,CAACgC,MAAM,CAAC;EAC/BH,QAAQ,EAAE;IACRI,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC;EACDH,YAAY,EAAE;IAAEE,KAAK,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAG,CAAC;EACxCJ,QAAQ,EAAE;IAAEG,KAAK,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAG;AACrC,CAAC,CAAC", "ignoreList": []}