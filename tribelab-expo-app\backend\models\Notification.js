import mongoose from 'mongoose';

const { Schema } = mongoose;

const notificationSchema = new Schema({
  recipient: { 
    type: String, 
    ref: 'User', 
    required: true 
  },
  sender: { 
    type: String, 
    ref: 'User' 
  },
  type: { 
    type: String, 
    enum: [
      'message', 
      'community_invite', 
      'course_enrollment', 
      'lesson_complete',
      'community_join_request',
      'community_join_approved',
      'community_join_rejected',
      'new_course',
      'new_lesson',
      'system'
    ],
    required: true 
  },
  title: { type: String, required: true },
  message: { type: String, required: true },
  
  // Related entities
  communityId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Community' 
  },
  courseId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Course' 
  },
  lessonId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Lesson' 
  },
  messageId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Message' 
  },
  
  // Notification status
  isRead: { type: Boolean, default: false },
  readAt: { type: Date },
  
  // Additional data
  data: { type: Schema.Types.Mixed },
  
  // Push notification status
  pushSent: { type: Boolean, default: false },
  pushSentAt: { type: Date },
  
  // Email notification status
  emailSent: { type: Boolean, default: false },
  emailSentAt: { type: Date }
}, { 
  timestamps: true 
});

// Create indexes for faster queries
notificationSchema.index({ recipient: 1, createdAt: -1 });
notificationSchema.index({ isRead: 1 });
notificationSchema.index({ type: 1 });
notificationSchema.index({ communityId: 1 });

export const Notification = mongoose.model('Notification', notificationSchema);
