import mongoose from 'mongoose';

const { Schema } = mongoose;

const courseSchema = new Schema({
  title: { type: String, required: true },
  description: { type: String },
  communityId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Community', 
    required: true 
  },
  createdBy: { 
    type: String, 
    ref: 'User', 
    required: true 
  },
  thumbnail: { type: String },
  isPublished: { type: Boolean, default: false },
  isPublic: { type: Boolean, default: false },
  enrolledUsers: [{ type: String, ref: 'User' }],
  tags: [{ type: String }],
  
  // Additional fields for mobile app
  category: { type: String },
  level: { 
    type: String, 
    enum: ['beginner', 'intermediate', 'advanced'],
    default: 'beginner'
  },
  duration: { type: Number, default: 0 }, // in minutes
  price: { type: Number, default: 0 },
  currency: { type: String, default: 'USD' },
  enrollmentCount: { type: Number, default: 0 },
  rating: { type: Number, default: 0 },
  reviewCount: { type: Number, default: 0 },
  
  // Instructor information
  instructor: {
    id: { type: String, ref: 'User' },
    name: { type: String },
    avatar: { type: String },
    bio: { type: String }
  }
}, { 
  timestamps: true 
});

// Create indexes for faster queries
courseSchema.index({ communityId: 1 });
courseSchema.index({ createdBy: 1 });
courseSchema.index({ isPublished: 1 });
courseSchema.index({ tags: 1 });
courseSchema.index({ category: 1 });
courseSchema.index({ level: 1 });

export const Course = mongoose.model('Course', courseSchema);
